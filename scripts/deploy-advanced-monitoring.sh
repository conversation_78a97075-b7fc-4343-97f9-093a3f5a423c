#!/bin/bash

# 🧠 SCRIPT DE DÉPLOIEMENT MONITORING HANUMAN AVANCÉ - JOUR 3-4 EXCELLENCE 10/10
# Déploiement du monitoring temps réel pour <PERSON><PERSON> et tous ses agents

set -euo pipefail

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
MONITORING_TIMESTAMP=$(date +%Y%m%d_%H%M%S)
MONITORING_DIR="${PROJECT_ROOT}/monitoring"
HANUMAN_DIR="${PROJECT_ROOT}/hanuman-unified"
LOGS_DIR="${PROJECT_ROOT}/logs/monitoring"

# Couleurs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# Initialisation
mkdir -p "$MONITORING_DIR" "$LOGS_DIR"

echo -e "${BLUE}🧠 DÉPLOIEMENT MONITORING HANUMAN AVANCÉ${NC}"
echo "=============================================="
echo "Timestamp: $MONITORING_TIMESTAMP"
echo "Monitoring Dir: $MONITORING_DIR"
echo ""

# Fonction de logging
log_monitoring() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")
            echo -e "${GREEN}[INFO]${NC} $message"
            ;;
        "WARN")
            echo -e "${YELLOW}[WARN]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
        "SUCCESS")
            echo -e "${PURPLE}[SUCCESS]${NC} $message"
            ;;
    esac
    
    echo "[$timestamp] [$level] $message" >> "${LOGS_DIR}/monitoring_deployment.log"
}

# 1. CONFIGURATION PROMETHEUS POUR HANUMAN
setup_prometheus_hanuman() {
    log_monitoring "INFO" "🔧 Configuration Prometheus pour Hanuman..."
    
    cat > "${MONITORING_DIR}/prometheus-hanuman.yml" << 'EOF'
global:
  scrape_interval: 5s
  evaluation_interval: 5s
  external_labels:
    monitor: 'hanuman-excellence-monitor'
    environment: 'production'

rule_files:
  - "hanuman_alert_rules.yml"
  - "excellence_alert_rules.yml"

scrape_configs:
  # Hanuman Cortex Central
  - job_name: 'hanuman-cortex-central'
    static_configs:
      - targets: ['localhost:3001']
    metrics_path: '/metrics'
    scrape_interval: 1s
    scrape_timeout: 1s
    
  # Agents Hanuman
  - job_name: 'hanuman-agents'
    static_configs:
      - targets: 
        - 'localhost:3010'  # Agent Security
        - 'localhost:3011'  # Agent Performance
        - 'localhost:3012'  # Agent QA
        - 'localhost:3013'  # Agent DevOps
        - 'localhost:3014'  # Agent Frontend
        - 'localhost:3015'  # Agent Evolution
    metrics_path: '/health/metrics'
    scrape_interval: 5s
    
  # Système Immunitaire Hanuman
  - job_name: 'hanuman-immune-system'
    static_configs:
      - targets: ['localhost:3020']
    metrics_path: '/immune/metrics'
    scrape_interval: 2s
    
  # Monitoring Système
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['localhost:9100']
      
  # Backend Retreat And Be
  - job_name: 'retreat-backend'
    static_configs:
      - targets: ['localhost:3000']
    metrics_path: '/api/metrics'
    
  # Frontend Retreat And Be
  - job_name: 'retreat-frontend'
    static_configs:
      - targets: ['localhost:3002']
    metrics_path: '/metrics'

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

EOF

    log_monitoring "SUCCESS" "✅ Configuration Prometheus Hanuman créée"
}

# 2. RÈGLES D'ALERTING HANUMAN
setup_hanuman_alerts() {
    log_monitoring "INFO" "🚨 Configuration des alertes Hanuman..."
    
    cat > "${MONITORING_DIR}/hanuman_alert_rules.yml" << 'EOF'
groups:
  - name: hanuman_cortex_alerts
    rules:
      - alert: HanumanCortexDown
        expr: up{job="hanuman-cortex-central"} == 0
        for: 10s
        labels:
          severity: critical
          component: cortex-central
        annotations:
          summary: "🚨 Hanuman Cortex Central est DOWN"
          description: "Le cerveau central de Hanuman ne répond plus depuis {{ $value }}s"
          
      - alert: HanumanCortexHighCPU
        expr: hanuman_cortex_cpu_usage > 80
        for: 30s
        labels:
          severity: warning
          component: cortex-central
        annotations:
          summary: "⚠️ CPU élevé sur Cortex Central"
          description: "CPU du Cortex Central: {{ $value }}%"
          
      - alert: HanumanCortexHighMemory
        expr: hanuman_cortex_memory_usage > 85
        for: 30s
        labels:
          severity: warning
          component: cortex-central
        annotations:
          summary: "⚠️ Mémoire élevée sur Cortex Central"
          description: "Mémoire du Cortex Central: {{ $value }}%"

  - name: hanuman_agents_alerts
    rules:
      - alert: HanumanAgentDown
        expr: up{job="hanuman-agents"} == 0
        for: 30s
        labels:
          severity: critical
          component: agent
        annotations:
          summary: "🚨 Agent Hanuman DOWN"
          description: "Agent {{ $labels.instance }} ne répond plus"
          
      - alert: HanumanAgentHighErrorRate
        expr: rate(hanuman_agent_errors_total[5m]) > 0.1
        for: 60s
        labels:
          severity: warning
          component: agent
        annotations:
          summary: "⚠️ Taux d'erreur élevé sur agent"
          description: "Agent {{ $labels.instance }}: {{ $value }} erreurs/sec"
          
      - alert: HanumanAgentSlowResponse
        expr: hanuman_agent_response_time > 1000
        for: 60s
        labels:
          severity: warning
          component: agent
        annotations:
          summary: "⚠️ Temps de réponse lent sur agent"
          description: "Agent {{ $labels.instance }}: {{ $value }}ms"

  - name: hanuman_immune_system_alerts
    rules:
      - alert: HanumanImmuneSystemDown
        expr: up{job="hanuman-immune-system"} == 0
        for: 15s
        labels:
          severity: critical
          component: immune-system
        annotations:
          summary: "🚨 Système Immunitaire Hanuman DOWN"
          description: "Le système de protection de Hanuman est inactif"
          
      - alert: HanumanSecurityThreatDetected
        expr: hanuman_security_threats_detected > 0
        for: 0s
        labels:
          severity: critical
          component: security
        annotations:
          summary: "🚨 MENACE SÉCURITÉ DÉTECTÉE"
          description: "{{ $value }} menaces détectées par le système immunitaire"
          
      - alert: HanumanAnomalyDetected
        expr: hanuman_anomaly_score > 0.8
        for: 30s
        labels:
          severity: warning
          component: anomaly-detection
        annotations:
          summary: "⚠️ Anomalie comportementale détectée"
          description: "Score d'anomalie: {{ $value }}"

EOF

    log_monitoring "SUCCESS" "✅ Règles d'alerting Hanuman configurées"
}

# 3. CONFIGURATION GRAFANA DASHBOARD
setup_grafana_dashboard() {
    log_monitoring "INFO" "📊 Configuration Dashboard Grafana Hanuman..."
    
    mkdir -p "${MONITORING_DIR}/grafana/dashboards"
    
    cat > "${MONITORING_DIR}/grafana/dashboards/hanuman-excellence-dashboard.json" << 'EOF'
{
  "dashboard": {
    "id": null,
    "title": "🧠 Hanuman Excellence 10/10 Dashboard",
    "tags": ["hanuman", "excellence", "monitoring"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "🧠 Cortex Central Status",
        "type": "stat",
        "targets": [
          {
            "expr": "up{job=\"hanuman-cortex-central\"}",
            "legendFormat": "Cortex Status"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "color": {
              "mode": "thresholds"
            },
            "thresholds": {
              "steps": [
                {"color": "red", "value": 0},
                {"color": "green", "value": 1}
              ]
            }
          }
        },
        "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}
      },
      {
        "id": 2,
        "title": "🤖 Agents Status",
        "type": "stat",
        "targets": [
          {
            "expr": "sum(up{job=\"hanuman-agents\"})",
            "legendFormat": "Active Agents"
          }
        ],
        "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}
      },
      {
        "id": 3,
        "title": "🛡️ Immune System",
        "type": "stat",
        "targets": [
          {
            "expr": "up{job=\"hanuman-immune-system\"}",
            "legendFormat": "Immune Status"
          }
        ],
        "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}
      },
      {
        "id": 4,
        "title": "🎯 Excellence Score",
        "type": "gauge",
        "targets": [
          {
            "expr": "hanuman_excellence_score",
            "legendFormat": "Score"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "min": 0,
            "max": 10,
            "thresholds": {
              "steps": [
                {"color": "red", "value": 0},
                {"color": "yellow", "value": 7},
                {"color": "green", "value": 9.5}
              ]
            }
          }
        },
        "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}
      },
      {
        "id": 5,
        "title": "📈 Performance Metrics",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(hanuman_requests_total[5m])",
            "legendFormat": "Requests/sec"
          },
          {
            "expr": "hanuman_response_time",
            "legendFormat": "Response Time (ms)"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
      },
      {
        "id": 6,
        "title": "🚨 Security Alerts",
        "type": "table",
        "targets": [
          {
            "expr": "ALERTS{job=~\"hanuman.*\"}",
            "legendFormat": "Alert"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "5s"
  }
}
EOF

    log_monitoring "SUCCESS" "✅ Dashboard Grafana Hanuman configuré"
}

# 4. SCRIPT DE DÉMARRAGE MONITORING
create_monitoring_startup() {
    log_monitoring "INFO" "🚀 Création script de démarrage monitoring..."
    
    cat > "${MONITORING_DIR}/start-hanuman-monitoring.sh" << 'EOF'
#!/bin/bash

# Script de démarrage monitoring Hanuman Excellence 10/10

set -euo pipefail

MONITORING_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$MONITORING_DIR")"

echo "🧠 Démarrage Monitoring Hanuman Excellence 10/10..."

# 1. Démarrage Prometheus
echo "📊 Démarrage Prometheus..."
if command -v prometheus &> /dev/null; then
    prometheus \
        --config.file="$MONITORING_DIR/prometheus-hanuman.yml" \
        --storage.tsdb.path="$MONITORING_DIR/prometheus-data" \
        --web.console.templates="$MONITORING_DIR/consoles" \
        --web.console.libraries="$MONITORING_DIR/console_libraries" \
        --web.listen-address=":9090" \
        --web.enable-lifecycle &
    echo "✅ Prometheus démarré sur :9090"
else
    echo "❌ Prometheus non installé"
    exit 1
fi

# 2. Démarrage Grafana (si installé)
if command -v grafana-server &> /dev/null; then
    echo "📈 Démarrage Grafana..."
    grafana-server \
        --homepath="/usr/share/grafana" \
        --config="/etc/grafana/grafana.ini" &
    echo "✅ Grafana démarré sur :3000"
fi

# 3. Démarrage Node Exporter (si installé)
if command -v node_exporter &> /dev/null; then
    echo "📊 Démarrage Node Exporter..."
    node_exporter &
    echo "✅ Node Exporter démarré sur :9100"
fi

# 4. Attendre que les services soient prêts
sleep 5

echo ""
echo "🎉 Monitoring Hanuman Excellence 10/10 démarré!"
echo "📊 Prometheus: http://localhost:9090"
echo "📈 Grafana: http://localhost:3000"
echo "🧠 Hanuman Dashboard: http://localhost:3000/d/hanuman-excellence"
echo ""

# Garder le script en vie
wait
EOF

    chmod +x "${MONITORING_DIR}/start-hanuman-monitoring.sh"
    log_monitoring "SUCCESS" "✅ Script de démarrage créé"
}

# 5. CONFIGURATION ALERTMANAGER
setup_alertmanager() {
    log_monitoring "INFO" "📢 Configuration AlertManager..."
    
    cat > "${MONITORING_DIR}/alertmanager.yml" << 'EOF'
global:
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'

route:
  group_by: ['alertname', 'component']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'hanuman-alerts'
  routes:
    - match:
        severity: critical
      receiver: 'hanuman-critical'
      group_wait: 5s
      repeat_interval: 5m

receivers:
  - name: 'hanuman-alerts'
    webhook_configs:
      - url: 'http://localhost:3001/api/alerts'
        title: 'Hanuman Alert'
        text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
        
  - name: 'hanuman-critical'
    webhook_configs:
      - url: 'http://localhost:3001/api/alerts/critical'
        title: 'HANUMAN CRITICAL ALERT'
        text: '🚨 {{ range .Alerts }}{{ .Annotations.summary }}: {{ .Annotations.description }}{{ end }}'
    email_configs:
      - to: '<EMAIL>'
        subject: '🚨 HANUMAN CRITICAL ALERT'
        body: |
          🚨 ALERTE CRITIQUE HANUMAN
          
          {{ range .Alerts }}
          Alerte: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Composant: {{ .Labels.component }}
          Temps: {{ .StartsAt }}
          {{ end }}

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'component']
EOF

    log_monitoring "SUCCESS" "✅ AlertManager configuré"
}

# 6. TESTS DE VALIDATION
validate_monitoring_setup() {
    log_monitoring "INFO" "🧪 Validation de la configuration monitoring..."
    
    local validation_score=0
    local max_score=100
    
    # Vérification fichiers de configuration
    local config_files=(
        "prometheus-hanuman.yml"
        "hanuman_alert_rules.yml"
        "alertmanager.yml"
        "start-hanuman-monitoring.sh"
    )
    
    for file in "${config_files[@]}"; do
        if [ -f "${MONITORING_DIR}/$file" ]; then
            validation_score=$((validation_score + 20))
            log_monitoring "INFO" "  ✅ $file configuré"
        else
            log_monitoring "ERROR" "  ❌ $file manquant"
        fi
    done
    
    # Vérification Dashboard Grafana
    if [ -f "${MONITORING_DIR}/grafana/dashboards/hanuman-excellence-dashboard.json" ]; then
        validation_score=$((validation_score + 20))
        log_monitoring "INFO" "  ✅ Dashboard Grafana configuré"
    else
        log_monitoring "ERROR" "  ❌ Dashboard Grafana manquant"
    fi
    
    # Génération rapport de validation
    cat > "${MONITORING_DIR}/monitoring-validation-report.md" << EOF
# 🧠 Rapport de Validation Monitoring Hanuman

**Date**: $(date '+%Y-%m-%d %H:%M:%S')  
**Score**: $validation_score/$max_score

## ✅ Composants Configurés

- Prometheus avec métriques Hanuman spécialisées
- Règles d'alerting pour Cortex Central et Agents
- Dashboard Grafana Excellence 10/10
- AlertManager avec notifications critiques
- Script de démarrage automatisé

## 🚀 Démarrage

\`\`\`bash
cd monitoring
./start-hanuman-monitoring.sh
\`\`\`

## 📊 Accès

- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3000
- **Dashboard Hanuman**: http://localhost:3000/d/hanuman-excellence

## 🎯 Métriques Surveillées

- Status Cortex Central (1s)
- Health Agents (5s)
- Système Immunitaire (2s)
- Score Excellence temps réel
- Détection anomalies
- Alertes sécurité

---

*Monitoring Excellence 10/10 - Hanuman Unified*
EOF

    if [ "$validation_score" -eq "$max_score" ]; then
        log_monitoring "SUCCESS" "🎉 Validation monitoring: $validation_score/$max_score - PARFAIT!"
    else
        log_monitoring "WARN" "⚠️ Validation monitoring: $validation_score/$max_score - À compléter"
    fi
}

# EXÉCUTION PRINCIPALE
main() {
    log_monitoring "INFO" "🚀 Démarrage déploiement monitoring Hanuman..."
    
    setup_prometheus_hanuman
    setup_hanuman_alerts
    setup_grafana_dashboard
    create_monitoring_startup
    setup_alertmanager
    validate_monitoring_setup
    
    echo ""
    echo -e "${GREEN}✅ MONITORING HANUMAN AVANCÉ DÉPLOYÉ${NC}"
    echo -e "${BLUE}📊 Configuration: $MONITORING_DIR${NC}"
    echo -e "${BLUE}🚀 Démarrage: $MONITORING_DIR/start-hanuman-monitoring.sh${NC}"
    echo ""
    echo -e "${PURPLE}🎯 MONITORING EXCELLENCE 10/10 PRÊT!${NC}"
}

# Vérification des prérequis
check_requirements() {
    log_monitoring "INFO" "🔍 Vérification des prérequis..."
    
    # Vérification répertoire Hanuman
    if [ ! -d "$HANUMAN_DIR" ]; then
        log_monitoring "ERROR" "Répertoire Hanuman non trouvé: $HANUMAN_DIR"
        exit 1
    fi
    
    log_monitoring "SUCCESS" "✅ Prérequis validés"
}

# Point d'entrée
check_requirements
main "$@"
