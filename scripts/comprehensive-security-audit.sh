#!/bin/bash

# 🔐 SCRIPT D'AUDIT DE SÉCURITÉ COMPLET - JOUR 1 EXCELLENCE 10/10
# Diagnostic sécurité complet pour identifier vulnérabilités critiques

set -euo pipefail

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
AUDIT_TIMESTAMP=$(date +%Y%m%d_%H%M%S)
AUDIT_DIR="${PROJECT_ROOT}/security-audit-reports"
REPORT_FILE="${AUDIT_DIR}/comprehensive_security_audit_${AUDIT_TIMESTAMP}.json"
SUMMARY_FILE="${AUDIT_DIR}/security_summary_${AUDIT_TIMESTAMP}.md"

# Couleurs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Initialisation
mkdir -p "$AUDIT_DIR"

echo -e "${BLUE}🔐 AUDIT DE SÉCURITÉ COMPLET - EXCELLENCE 10/10${NC}"
echo "=================================================="
echo "Timestamp: $AUDIT_TIMESTAMP"
echo "Rapport: $REPORT_FILE"
echo ""

# Structure du rapport JSON
cat > "$REPORT_FILE" << EOF
{
  "audit_metadata": {
    "timestamp": "$AUDIT_TIMESTAMP",
    "project": "Retreat And Be - Excellence 10/10",
    "phase": "Jour 1 - Diagnostic Sécurité",
    "auditor": "Comprehensive Security Audit Script"
  },
  "services_audited": {},
  "vulnerability_summary": {
    "total": 0,
    "critical": 0,
    "high": 0,
    "medium": 0,
    "low": 0
  },
  "security_checks": {},
  "recommendations": [],
  "compliance_status": {}
}
EOF

# Fonction de logging
log_audit() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")
            echo -e "${GREEN}[INFO]${NC} $message"
            ;;
        "WARN")
            echo -e "${YELLOW}[WARN]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
        "CRITICAL")
            echo -e "${RED}[CRITICAL]${NC} $message"
            ;;
    esac
    
    echo "[$timestamp] [$level] $message" >> "${AUDIT_DIR}/audit.log"
}

# 1. AUDIT NPM DEPENDENCIES
audit_npm_dependencies() {
    log_audit "INFO" "🔍 Audit des dépendances NPM..."
    
    local services=(
        "Projet-RB2/Backend-NestJS"
        "Projet-RB2/Front-Audrey-V1-Main-main"
        "hanuman-unified"
        "hanuman-unified/brain/cortex-central"
        "hanuman-unified/agents/security"
        "hanuman-unified/agents/performance"
    )
    
    local total_vulns=0
    local critical_vulns=0
    local high_vulns=0
    
    for service in "${services[@]}"; do
        local service_path="${PROJECT_ROOT}/$service"
        if [ -f "$service_path/package.json" ]; then
            log_audit "INFO" "  Audit de $service..."
            
            cd "$service_path"
            if npm audit --json > /tmp/npm_audit_$$.json 2>/dev/null; then
                local vulns=$(jq -r '.metadata.vulnerabilities.total // 0' /tmp/npm_audit_$$.json)
                local crit=$(jq -r '.metadata.vulnerabilities.critical // 0' /tmp/npm_audit_$$.json)
                local high=$(jq -r '.metadata.vulnerabilities.high // 0' /tmp/npm_audit_$$.json)
                
                total_vulns=$((total_vulns + vulns))
                critical_vulns=$((critical_vulns + crit))
                high_vulns=$((high_vulns + high))
                
                if [ "$crit" -gt 0 ] || [ "$high" -gt 0 ]; then
                    log_audit "CRITICAL" "    $service: $crit critiques, $high hautes"
                else
                    log_audit "INFO" "    $service: $vulns vulnérabilités total"
                fi
            else
                log_audit "WARN" "    $service: Impossible d'exécuter npm audit"
            fi
            
            rm -f /tmp/npm_audit_$$.json
        fi
    done
    
    # Mise à jour du rapport JSON
    jq --argjson total "$total_vulns" \
       --argjson critical "$critical_vulns" \
       --argjson high "$high_vulns" \
       '.vulnerability_summary.total = $total | 
        .vulnerability_summary.critical = $critical | 
        .vulnerability_summary.high = $high' \
       "$REPORT_FILE" > /tmp/report_$$.json && mv /tmp/report_$$.json "$REPORT_FILE"
    
    log_audit "INFO" "✅ Audit NPM terminé: $total_vulns vulnérabilités ($critical_vulns critiques)"
}

# 2. AUDIT CONFIGURATION SÉCURITÉ
audit_security_config() {
    log_audit "INFO" "🔧 Audit des configurations de sécurité..."
    
    local security_score=0
    local max_score=100
    
    # Vérification HTTPS/TLS
    if grep -r "https://" "${PROJECT_ROOT}/Projet-RB2" >/dev/null 2>&1; then
        security_score=$((security_score + 20))
        log_audit "INFO" "  ✅ Configuration HTTPS détectée"
    else
        log_audit "WARN" "  ⚠️ Configuration HTTPS manquante"
    fi
    
    # Vérification Helmet.js
    if grep -r "helmet" "${PROJECT_ROOT}/Projet-RB2/Backend-NestJS" >/dev/null 2>&1; then
        security_score=$((security_score + 15))
        log_audit "INFO" "  ✅ Helmet.js configuré"
    else
        log_audit "WARN" "  ⚠️ Helmet.js manquant"
    fi
    
    # Vérification CORS
    if grep -r "cors" "${PROJECT_ROOT}/Projet-RB2/Backend-NestJS" >/dev/null 2>&1; then
        security_score=$((security_score + 15))
        log_audit "INFO" "  ✅ CORS configuré"
    else
        log_audit "WARN" "  ⚠️ Configuration CORS manquante"
    fi
    
    # Vérification Rate Limiting
    if grep -r "rate.*limit" "${PROJECT_ROOT}/Projet-RB2/Backend-NestJS" >/dev/null 2>&1; then
        security_score=$((security_score + 20))
        log_audit "INFO" "  ✅ Rate limiting configuré"
    else
        log_audit "WARN" "  ⚠️ Rate limiting manquant"
    fi
    
    # Vérification JWT
    if grep -r "jwt" "${PROJECT_ROOT}/Projet-RB2/Backend-NestJS" >/dev/null 2>&1; then
        security_score=$((security_score + 15))
        log_audit "INFO" "  ✅ JWT configuré"
    else
        log_audit "WARN" "  ⚠️ JWT manquant"
    fi
    
    # Vérification variables d'environnement
    if [ -f "${PROJECT_ROOT}/Projet-RB2/Backend-NestJS/.env.example" ]; then
        security_score=$((security_score + 15))
        log_audit "INFO" "  ✅ Template .env.example présent"
    else
        log_audit "WARN" "  ⚠️ Template .env.example manquant"
    fi
    
    # Mise à jour du rapport
    jq --argjson score "$security_score" \
       --argjson max "$max_score" \
       '.security_checks.configuration_score = $score | 
        .security_checks.configuration_max = $max' \
       "$REPORT_FILE" > /tmp/report_$$.json && mv /tmp/report_$$.json "$REPORT_FILE"
    
    log_audit "INFO" "✅ Audit configuration: $security_score/$max_score points"
}

# 3. AUDIT SECRETS ET CREDENTIALS
audit_secrets() {
    log_audit "INFO" "🔑 Audit des secrets et credentials..."
    
    local secrets_found=0
    local patterns=(
        "password.*=.*['\"][^'\"]{3,}"
        "api.*key.*=.*['\"][^'\"]{10,}"
        "secret.*=.*['\"][^'\"]{10,}"
        "token.*=.*['\"][^'\"]{10,}"
        "private.*key"
        "-----BEGIN.*PRIVATE.*KEY-----"
    )
    
    for pattern in "${patterns[@]}"; do
        local matches=$(grep -r -i "$pattern" "${PROJECT_ROOT}" \
            --exclude-dir=node_modules \
            --exclude-dir=.git \
            --exclude="*.log" \
            --exclude="*.json" 2>/dev/null | wc -l)
        
        if [ "$matches" -gt 0 ]; then
            secrets_found=$((secrets_found + matches))
            log_audit "CRITICAL" "  🚨 Pattern '$pattern': $matches occurrences"
        fi
    done
    
    # Vérification fichiers .env
    local env_files=$(find "${PROJECT_ROOT}" -name ".env*" -not -name ".env.example" 2>/dev/null | wc -l)
    if [ "$env_files" -gt 0 ]; then
        log_audit "WARN" "  ⚠️ $env_files fichiers .env trouvés (vérifier qu'ils ne sont pas commitées)"
    fi
    
    # Mise à jour du rapport
    jq --argjson secrets "$secrets_found" \
       '.security_checks.exposed_secrets = $secrets' \
       "$REPORT_FILE" > /tmp/report_$$.json && mv /tmp/report_$$.json "$REPORT_FILE"
    
    if [ "$secrets_found" -eq 0 ]; then
        log_audit "INFO" "✅ Aucun secret exposé détecté"
    else
        log_audit "CRITICAL" "🚨 $secrets_found secrets potentiellement exposés"
    fi
}

# 4. AUDIT INFRASTRUCTURE KUBERNETES
audit_k8s_security() {
    log_audit "INFO" "☸️ Audit sécurité Kubernetes..."
    
    local k8s_score=0
    local k8s_dir="${PROJECT_ROOT}/k8s"
    
    if [ -d "$k8s_dir" ]; then
        # Vérification Network Policies
        if find "$k8s_dir" -name "*network*policy*" -o -name "*netpol*" | grep -q .; then
            k8s_score=$((k8s_score + 25))
            log_audit "INFO" "  ✅ Network Policies configurées"
        else
            log_audit "WARN" "  ⚠️ Network Policies manquantes"
        fi
        
        # Vérification Pod Security Policies
        if find "$k8s_dir" -name "*pod*security*" -o -name "*psp*" | grep -q .; then
            k8s_score=$((k8s_score + 25))
            log_audit "INFO" "  ✅ Pod Security Policies configurées"
        else
            log_audit "WARN" "  ⚠️ Pod Security Policies manquantes"
        fi
        
        # Vérification RBAC
        if find "$k8s_dir" -name "*rbac*" -o -name "*role*" | grep -q .; then
            k8s_score=$((k8s_score + 25))
            log_audit "INFO" "  ✅ RBAC configuré"
        else
            log_audit "WARN" "  ⚠️ RBAC manquant"
        fi
        
        # Vérification Secrets K8s
        if find "$k8s_dir" -name "*secret*" | grep -q .; then
            k8s_score=$((k8s_score + 25))
            log_audit "INFO" "  ✅ Secrets Kubernetes configurés"
        else
            log_audit "WARN" "  ⚠️ Secrets Kubernetes manquants"
        fi
    else
        log_audit "WARN" "  ⚠️ Répertoire Kubernetes non trouvé"
    fi
    
    # Mise à jour du rapport
    jq --argjson score "$k8s_score" \
       '.security_checks.kubernetes_security_score = $score' \
       "$REPORT_FILE" > /tmp/report_$$.json && mv /tmp/report_$$.json "$REPORT_FILE"
    
    log_audit "INFO" "✅ Audit K8s: $k8s_score/100 points"
}

# 5. GÉNÉRATION DU RAPPORT FINAL
generate_summary_report() {
    log_audit "INFO" "📊 Génération du rapport de synthèse..."
    
    local total_vulns=$(jq -r '.vulnerability_summary.total' "$REPORT_FILE")
    local critical_vulns=$(jq -r '.vulnerability_summary.critical' "$REPORT_FILE")
    local high_vulns=$(jq -r '.vulnerability_summary.high' "$REPORT_FILE")
    local config_score=$(jq -r '.security_checks.configuration_score // 0' "$REPORT_FILE")
    local k8s_score=$(jq -r '.security_checks.kubernetes_security_score // 0' "$REPORT_FILE")
    local secrets=$(jq -r '.security_checks.exposed_secrets // 0' "$REPORT_FILE")
    
    # Calcul du score global
    local global_score=0
    if [ "$critical_vulns" -eq 0 ]; then global_score=$((global_score + 30)); fi
    if [ "$high_vulns" -eq 0 ]; then global_score=$((global_score + 20)); fi
    global_score=$((global_score + config_score * 30 / 100))
    global_score=$((global_score + k8s_score * 20 / 100))
    
    # Génération du rapport Markdown
    cat > "$SUMMARY_FILE" << EOF
# 🔐 RAPPORT D'AUDIT DE SÉCURITÉ COMPLET

**Date**: $(date '+%Y-%m-%d %H:%M:%S')  
**Phase**: Jour 1 - Diagnostic Sécurité Excellence 10/10  
**Score Global**: $global_score/100

## 📊 RÉSUMÉ EXÉCUTIF

| Métrique | Valeur | Status |
|----------|--------|--------|
| **Vulnérabilités Total** | $total_vulns | $([ "$total_vulns" -eq 0 ] && echo "✅" || echo "⚠️") |
| **Vulnérabilités Critiques** | $critical_vulns | $([ "$critical_vulns" -eq 0 ] && echo "✅" || echo "🚨") |
| **Vulnérabilités Hautes** | $high_vulns | $([ "$high_vulns" -eq 0 ] && echo "✅" || echo "⚠️") |
| **Configuration Sécurité** | $config_score/100 | $([ "$config_score" -ge 80 ] && echo "✅" || echo "⚠️") |
| **Sécurité Kubernetes** | $k8s_score/100 | $([ "$k8s_score" -ge 80 ] && echo "✅" || echo "⚠️") |
| **Secrets Exposés** | $secrets | $([ "$secrets" -eq 0 ] && echo "✅" || echo "🚨") |

## 🎯 ACTIONS PRIORITAIRES

### 🔴 CRITIQUE (À corriger immédiatement)
$([ "$critical_vulns" -gt 0 ] && echo "- $critical_vulns vulnérabilités critiques à patcher" || echo "- ✅ Aucune vulnérabilité critique")
$([ "$secrets" -gt 0 ] && echo "- $secrets secrets exposés à sécuriser" || echo "- ✅ Aucun secret exposé")

### 🟠 HAUTE PRIORITÉ (Cette semaine)
$([ "$high_vulns" -gt 0 ] && echo "- $high_vulns vulnérabilités hautes à corriger" || echo "- ✅ Aucune vulnérabilité haute")
$([ "$config_score" -lt 80 ] && echo "- Configuration sécurité à renforcer ($config_score/100)" || echo "- ✅ Configuration sécurité satisfaisante")

### 🟡 MOYENNE PRIORITÉ (Ce mois)
$([ "$k8s_score" -lt 80 ] && echo "- Sécurité Kubernetes à améliorer ($k8s_score/100)" || echo "- ✅ Sécurité Kubernetes satisfaisante")

## 📋 PROCHAINES ÉTAPES JOUR 1

1. **Correction vulnérabilités critiques** (si présentes)
2. **Sécurisation des secrets** exposés
3. **Mise à jour dépendances** vulnérables
4. **Renforcement configuration** sécurité

---

*Rapport généré automatiquement - Audit Excellence 10/10*
EOF

    log_audit "INFO" "✅ Rapport de synthèse généré: $SUMMARY_FILE"
}

# EXÉCUTION PRINCIPALE
main() {
    log_audit "INFO" "🚀 Démarrage audit sécurité complet..."
    
    audit_npm_dependencies
    audit_security_config
    audit_secrets
    audit_k8s_security
    generate_summary_report
    
    echo ""
    echo -e "${GREEN}✅ AUDIT DE SÉCURITÉ TERMINÉ${NC}"
    echo -e "${BLUE}📊 Rapport JSON: $REPORT_FILE${NC}"
    echo -e "${BLUE}📋 Synthèse: $SUMMARY_FILE${NC}"
    echo ""
    
    # Affichage du score final
    local global_score=$(jq -r '.security_checks.configuration_score // 0' "$REPORT_FILE")
    if [ "$global_score" -ge 80 ]; then
        echo -e "${GREEN}🎉 Score sécurité: $global_score/100 - EXCELLENT${NC}"
    elif [ "$global_score" -ge 60 ]; then
        echo -e "${YELLOW}⚠️ Score sécurité: $global_score/100 - À AMÉLIORER${NC}"
    else
        echo -e "${RED}🚨 Score sécurité: $global_score/100 - CRITIQUE${NC}"
    fi
}

# Vérification des outils requis
check_requirements() {
    local tools=("jq" "npm" "grep" "find")
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            log_audit "ERROR" "Outil manquant: $tool"
            echo "Installez avec: sudo apt-get install $tool (Ubuntu/Debian) ou brew install $tool (macOS)"
            exit 1
        fi
    done
}

# Point d'entrée
check_requirements
main "$@"
