/**
 * 🔄 HANUMAN - Service de Recovery Automatique
 * ===========================================
 * Recovery automatique des agents en cas de panne
 */

const fs = require('fs').promises;
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

class RecoveryService {
    constructor() {
        this.backupDir = path.join(__dirname, '..', 'agents');
        this.recoveryTimeout = 2 * 60 * 1000; // 2 minutes max pour recovery
        this.isRecovering = false;
    }

    // Récupérer un agent depuis le backup le plus récent
    async recoverAgent(agentName) {
        if (this.isRecovering) {
            console.log('⚠️  Recovery déjà en cours, attente...');
            return false;
        }

        this.isRecovering = true;
        const startTime = Date.now();

        console.log(`🔄 Démarrage recovery de l'agent: ${agentName}`);

        try {
            // Trouver le backup le plus récent
            const latestBackup = await this.findLatestBackup(agentName);
            if (!latestBackup) {
                throw new Error(`Aucun backup trouvé pour l'agent ${agentName}`);
            }

            console.log(`📁 Backup trouvé: ${latestBackup}`);

            // Restaurer les fichiers
            await this.restoreAgentFiles(agentName, latestBackup);

            // Restaurer l'état
            await this.restoreAgentState(agentName, latestBackup);

            // Redémarrer l'agent
            await this.restartAgent(agentName);

            const recoveryTime = Date.now() - startTime;
            console.log(`✅ Recovery de ${agentName} terminé en ${recoveryTime}ms`);

            return true;

        } catch (error) {
            console.error(`❌ Erreur recovery ${agentName}:`, error.message);
            return false;
        } finally {
            this.isRecovering = false;
        }
    }

    // Trouver le backup le plus récent pour un agent
    async findLatestBackup(agentName) {
        try {
            const items = await fs.readdir(this.backupDir, { withFileTypes: true });
            const backupDirs = items
                .filter(item => item.isDirectory())
                .map(item => item.name)
                .sort()
                .reverse();

            for (const backupDir of backupDirs) {
                const agentBackupPath = path.join(this.backupDir, backupDir, agentName);
                try {
                    await fs.access(agentBackupPath);
                    return path.join(this.backupDir, backupDir);
                } catch {
                    continue;
                }
            }

            return null;
        } catch (error) {
            console.error('Erreur recherche backup:', error.message);
            return null;
        }
    }

    // Restaurer les fichiers d'un agent
    async restoreAgentFiles(agentName, backupPath) {
        console.log(`📂 Restauration des fichiers de ${agentName}...`);

        const agentBackupPath = path.join(backupPath, agentName);
        const targetPaths = [
            path.join(__dirname, '..', '..', 'specialized-agents', agentName),
            path.join(__dirname, '..', '..', 'cortex-central'),
            path.join(__dirname, '..', '..', 'immune-system')
        ];

        try {
            const files = await fs.readdir(agentBackupPath);

            for (const file of files) {
                if (file === 'state.json' || file === 'models.json') continue;

                const sourcePath = path.join(agentBackupPath, file);

                // Trouver le bon répertoire de destination
                for (const targetPath of targetPaths) {
                    try {
                        await fs.access(targetPath);
                        const destPath = path.join(targetPath, file);
                        await fs.copyFile(sourcePath, destPath);
                        console.log(`  ✅ Restauré: ${file}`);
                        break;
                    } catch {
                        continue;
                    }
                }
            }

        } catch (error) {
            console.error(`Erreur restauration fichiers ${agentName}:`, error.message);
        }
    }

    // Restaurer l'état d'un agent
    async restoreAgentState(agentName, backupPath) {
        console.log(`🧠 Restauration de l'état de ${agentName}...`);

        try {
            const statePath = path.join(backupPath, agentName, 'state.json');
            const stateData = await fs.readFile(statePath, 'utf8');
            const state = JSON.parse(stateData);

            console.log(`  ✅ État restauré: ${state.timestamp}`);

            // Restaurer les modèles IA si disponibles
            const modelsPath = path.join(backupPath, agentName, 'models.json');
            try {
                const modelsData = await fs.readFile(modelsPath, 'utf8');
                const models = JSON.parse(modelsData);
                console.log(`  ✅ Modèles IA restaurés: ${models.timestamp}`);
            } catch {
                // Pas de modèles, continuer
            }

        } catch (error) {
            console.error(`Erreur restauration état ${agentName}:`, error.message);
        }
    }

    // Redémarrer un agent
    async restartAgent(agentName) {
        console.log(`🚀 Redémarrage de l'agent ${agentName}...`);

        try {
            // Simuler le redémarrage (en production, utiliser les vrais commandes)
            await new Promise(resolve => setTimeout(resolve, 1000));

            console.log(`  ✅ Agent ${agentName} redémarré`);

        } catch (error) {
            console.error(`Erreur redémarrage ${agentName}:`, error.message);
        }
    }

    // Recovery complet du système
    async performFullRecovery() {
        console.log('🔄 Démarrage du recovery complet du système...');

        const agents = [
            'cortex-central',
            'specialized-agents',
            'immune-system',
            'anomaly-detection',
            'health-checks'
        ];

        const results = [];

        for (const agent of agents) {
            const success = await this.recoverAgent(agent);
            results.push({ agent, success });
        }

        const successCount = results.filter(r => r.success).length;
        console.log(`✅ Recovery complet terminé: ${successCount}/${agents.length} agents récupérés`);

        return results;
    }
}

module.exports = RecoveryService;

// Démarrage automatique si exécuté directement
if (require.main === module) {
    const recoveryService = new RecoveryService();

    // Exemple d'utilisation
    const agentToRecover = process.argv[2];
    if (agentToRecover) {
        recoveryService.recoverAgent(agentToRecover);
    } else {
        console.log('Usage: node recovery-service.js <agent-name>');
        console.log('Ou: node recovery-service.js full (pour recovery complet)');
    }
}
