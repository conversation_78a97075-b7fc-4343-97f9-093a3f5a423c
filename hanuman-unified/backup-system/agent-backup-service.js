/**
 * 💾 HANUMAN - Service de Backup des Agents
 * ========================================
 * Sauvegarde continue des états et modèles IA des agents
 */

const fs = require('fs').promises;
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

class AgentBackupService {
    constructor() {
        this.backupInterval = 5 * 60 * 1000; // 5 minutes
        this.retentionDays = 7; // Garder 7 jours de backups
        this.backupDir = path.join(__dirname, 'agents');
        this.isRunning = false;
        this.agents = [
            'cortex-central',
            'specialized-agents',
            'immune-system',
            'anomaly-detection',
            'health-checks'
        ];
    }

    // Démarrer le service de backup
    async startBackupService() {
        console.log('💾 Démarrage du service de backup des agents...');

        this.isRunning = true;

        // Backup initial
        await this.performFullBackup();

        // Programmer les backups réguliers
        this.backupTimer = setInterval(async () => {
            if (this.isRunning) {
                await this.performIncrementalBackup();
            }
        }, this.backupInterval);

        // Nettoyage quotidien des anciens backups
        this.cleanupTimer = setInterval(async () => {
            if (this.isRunning) {
                await this.cleanupOldBackups();
            }
        }, 24 * 60 * 60 * 1000); // 24 heures

        console.log('✅ Service de backup des agents démarré');
    }

    // Arrêter le service de backup
    stopBackupService() {
        console.log('🛑 Arrêt du service de backup des agents...');

        this.isRunning = false;

        if (this.backupTimer) {
            clearInterval(this.backupTimer);
        }

        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
        }

        console.log('✅ Service de backup arrêté');
    }

    // Effectuer un backup complet
    async performFullBackup() {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupPath = path.join(this.backupDir, `full-backup-${timestamp}`);

        console.log(`💾 Backup complet vers: ${backupPath}`);

        try {
            await fs.mkdir(backupPath, { recursive: true });

            for (const agent of this.agents) {
                await this.backupAgent(agent, backupPath, 'full');
            }

            // Créer un manifeste du backup
            const manifest = {
                type: 'full',
                timestamp: new Date().toISOString(),
                agents: this.agents,
                size: await this.calculateBackupSize(backupPath),
                checksum: await this.calculateChecksum(backupPath)
            };

            await fs.writeFile(
                path.join(backupPath, 'manifest.json'),
                JSON.stringify(manifest, null, 2)
            );

            console.log(`✅ Backup complet terminé: ${manifest.size} bytes`);
            return backupPath;

        } catch (error) {
            console.error('❌ Erreur backup complet:', error.message);
            throw error;
        }
    }

    // Effectuer un backup incrémental
    async performIncrementalBackup() {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupPath = path.join(this.backupDir, `incremental-backup-${timestamp}`);

        console.log(`💾 Backup incrémental vers: ${backupPath}`);

        try {
            await fs.mkdir(backupPath, { recursive: true });

            let hasChanges = false;

            for (const agent of this.agents) {
                const changed = await this.backupAgent(agent, backupPath, 'incremental');
                if (changed) hasChanges = true;
            }

            if (hasChanges) {
                // Créer un manifeste du backup
                const manifest = {
                    type: 'incremental',
                    timestamp: new Date().toISOString(),
                    agents: this.agents,
                    size: await this.calculateBackupSize(backupPath),
                    checksum: await this.calculateChecksum(backupPath)
                };

                await fs.writeFile(
                    path.join(backupPath, 'manifest.json'),
                    JSON.stringify(manifest, null, 2)
                );

                console.log(`✅ Backup incrémental terminé: ${manifest.size} bytes`);
            } else {
                // Supprimer le dossier vide si aucun changement
                await fs.rmdir(backupPath);
                console.log('ℹ️  Aucun changement détecté - backup incrémental ignoré');
            }

        } catch (error) {
            console.error('❌ Erreur backup incrémental:', error.message);
        }
    }

    // Sauvegarder un agent spécifique
    async backupAgent(agentName, backupPath, type) {
        const agentDir = path.join(backupPath, agentName);
        await fs.mkdir(agentDir, { recursive: true });

        let hasChanges = false;

        try {
            // Backup des fichiers de configuration
            const configFiles = await this.findAgentFiles(agentName, ['.js', '.json', '.yml', '.yaml']);
            for (const file of configFiles) {
                if (type === 'full' || await this.hasFileChanged(file)) {
                    await this.copyFile(file, path.join(agentDir, path.basename(file)));
                    hasChanges = true;
                }
            }

            // Backup des données d'état (si l'agent est actif)
            const stateData = await this.captureAgentState(agentName);
            if (stateData) {
                await fs.writeFile(
                    path.join(agentDir, 'state.json'),
                    JSON.stringify(stateData, null, 2)
                );
                hasChanges = true;
            }

            // Backup des modèles IA (si applicable)
            const modelData = await this.captureAgentModels(agentName);
            if (modelData) {
                await fs.writeFile(
                    path.join(agentDir, 'models.json'),
                    JSON.stringify(modelData, null, 2)
                );
                hasChanges = true;
            }

            if (hasChanges) {
                console.log(`  ✅ Agent ${agentName} sauvegardé`);
            }

            return hasChanges;

        } catch (error) {
            console.error(`  ❌ Erreur backup agent ${agentName}:`, error.message);
            return false;
        }
    }

    // Trouver les fichiers d'un agent
    async findAgentFiles(agentName, extensions) {
        const files = [];
        const searchPaths = [
            path.join(__dirname, '..', 'specialized-agents', agentName),
            path.join(__dirname, '..', 'cortex-central'),
            path.join(__dirname, '..', 'immune-system'),
            path.join(__dirname, '..', 'monitoring', 'predictive-alerts')
        ];

        for (const searchPath of searchPaths) {
            try {
                const items = await fs.readdir(searchPath, { withFileTypes: true });
                for (const item of items) {
                    if (item.isFile()) {
                        const ext = path.extname(item.name);
                        if (extensions.includes(ext)) {
                            files.push(path.join(searchPath, item.name));
                        }
                    }
                }
            } catch (error) {
                // Dossier n'existe pas, continuer
            }
        }

        return files;
    }

    // Capturer l'état d'un agent
    async captureAgentState(agentName) {
        try {
            // Simuler la capture d'état (en production, connecter aux vrais agents)
            const state = {
                agentName: agentName,
                timestamp: new Date().toISOString(),
                status: 'active',
                metrics: {
                    uptime: Math.floor(Math.random() * 86400),
                    memoryUsage: Math.floor(Math.random() * 100),
                    cpuUsage: Math.floor(Math.random() * 100)
                },
                configuration: {
                    version: '1.0.0',
                    lastUpdate: new Date().toISOString()
                }
            };

            return state;
        } catch (error) {
            console.error(`Erreur capture état ${agentName}:`, error.message);
            return null;
        }
    }

    // Capturer les modèles IA d'un agent
    async captureAgentModels(agentName) {
        if (agentName === 'anomaly-detection') {
            try {
                // Simuler la capture des modèles IA
                const models = {
                    agentName: agentName,
                    timestamp: new Date().toISOString(),
                    models: {
                        anomalyDetection: {
                            type: 'statistical',
                            parameters: {
                                threshold: 2.0,
                                windowSize: 50
                            },
                            trainingData: 'compressed_data_placeholder'
                        }
                    }
                };

                return models;
            } catch (error) {
                console.error(`Erreur capture modèles ${agentName}:`, error.message);
                return null;
            }
        }

        return null;
    }

    // Vérifier si un fichier a changé
    async hasFileChanged(filePath) {
        try {
            const stats = await fs.stat(filePath);
            const lastModified = stats.mtime.getTime();
            const fiveMinutesAgo = Date.now() - (5 * 60 * 1000);

            return lastModified > fiveMinutesAgo;
        } catch (error) {
            return false;
        }
    }

    // Copier un fichier
    async copyFile(source, destination) {
        try {
            await fs.copyFile(source, destination);
        } catch (error) {
            console.error(`Erreur copie ${source} -> ${destination}:`, error.message);
        }
    }

    // Calculer la taille d'un backup
    async calculateBackupSize(backupPath) {
        try {
            const { stdout } = await execAsync(`du -sb "${backupPath}"`);
            return parseInt(stdout.split('\t')[0]);
        } catch (error) {
            return 0;
        }
    }

    // Calculer le checksum d'un backup
    async calculateChecksum(backupPath) {
        try {
            const { stdout } = await execAsync(`find "${backupPath}" -type f -exec md5sum {} \\; | md5sum`);
            return stdout.split(' ')[0];
        } catch (error) {
            return 'unknown';
        }
    }

    // Nettoyer les anciens backups
    async cleanupOldBackups() {
        console.log('🧹 Nettoyage des anciens backups...');

        try {
            const items = await fs.readdir(this.backupDir, { withFileTypes: true });
            const cutoffDate = new Date(Date.now() - (this.retentionDays * 24 * 60 * 60 * 1000));

            let deletedCount = 0;

            for (const item of items) {
                if (item.isDirectory()) {
                    const itemPath = path.join(this.backupDir, item.name);
                    const stats = await fs.stat(itemPath);

                    if (stats.mtime < cutoffDate) {
                        await fs.rmdir(itemPath, { recursive: true });
                        deletedCount++;
                        console.log(`  🗑️  Supprimé: ${item.name}`);
                    }
                }
            }

            console.log(`✅ Nettoyage terminé: ${deletedCount} backup(s) supprimé(s)`);

        } catch (error) {
            console.error('❌ Erreur nettoyage:', error.message);
        }
    }

    // Obtenir le statut du service
    getStatus() {
        return {
            isRunning: this.isRunning,
            backupInterval: this.backupInterval,
            retentionDays: this.retentionDays,
            agents: this.agents,
            lastBackup: this.lastBackup || 'N/A'
        };
    }
}

module.exports = AgentBackupService;

// Démarrage automatique si exécuté directement
if (require.main === module) {
    const backupService = new AgentBackupService();

    backupService.startBackupService();

    // Afficher le statut toutes les 10 minutes
    setInterval(() => {
        console.log('📊 Statut backup service:', backupService.getStatus());
    }, 10 * 60 * 1000);

    // Gestion propre de l'arrêt
    process.on('SIGINT', () => {
        console.log('\n🛑 Arrêt du service de backup...');
        backupService.stopBackupService();
        process.exit(0);
    });
}
