#!/bin/bash

echo "🛑 Arrêt du service de backup Hanuman..."

if [[ -f backup-service.pid ]]; then
    PID=$(cat backup-service.pid)
    if ps -p $PID > /dev/null 2>&1; then
        kill $PID
        echo "✅ Service de backup arrêté (PID: $PID)"
        rm backup-service.pid
    else
        echo "⚠️  Service de backup déjà arrêté"
        rm backup-service.pid
    fi
else
    echo "⚠️  Fichier PID non trouvé"
fi
