/**
 * 🏥 HANUMAN - Health Checks Avancés
 * =================================
 * Système de surveillance de santé avec IA prédictive
 */

const http = require('http');
const https = require('https');
const { exec } = require('child_process');
const AnomalyDetectionAI = require('./anomaly-detection');

class AdvancedHealthChecker {
    constructor() {
        this.anomalyDetector = new AnomalyDetectionAI();
        this.healthHistory = [];
        this.checkInterval = 5000; // 5 secondes
        this.services = [
            {
                name: 'cortex-central',
                url: 'http://localhost:3001/health',
                critical: true,
                timeout: 2000
            },
            {
                name: 'specialized-agents',
                url: 'http://localhost:3002/health',
                critical: true,
                timeout: 3000
            },
            {
                name: 'immune-system',
                url: 'http://localhost:3003/health',
                critical: false,
                timeout: 2000
            }
        ];
    }

    // Démarrer la surveillance continue
    startContinuousMonitoring() {
        console.log('🏥 Démarrage des health checks avancés...');

        setInterval(async () => {
            await this.performHealthChecks();
        }, this.checkInterval);

        // Health check initial
        this.performHealthChecks();
    }

    // Effectuer tous les health checks
    async performHealthChecks() {
        const timestamp = new Date().toISOString();
        const healthReport = {
            timestamp,
            services: [],
            systemMetrics: await this.getSystemMetrics(),
            overallHealth: 'healthy'
        };

        // Vérifier chaque service
        for (const service of this.services) {
            const serviceHealth = await this.checkServiceHealth(service);
            healthReport.services.push(serviceHealth);

            if (serviceHealth.status === 'unhealthy' && service.critical) {
                healthReport.overallHealth = 'critical';
            } else if (serviceHealth.status === 'degraded') {
                healthReport.overallHealth = 'degraded';
            }
        }

        // Analyser avec l'IA
        const anomalies = await this.anomalyDetector.analyzeMetrics(healthReport.systemMetrics);
        healthReport.anomalies = anomalies;

        // Stocker l'historique
        this.healthHistory.push(healthReport);
        if (this.healthHistory.length > 1000) {
            this.healthHistory = this.healthHistory.slice(-1000);
        }

        // Déclencher des alertes si nécessaire
        await this.triggerAlertsIfNeeded(healthReport);

        return healthReport;
    }

    // Vérifier la santé d'un service spécifique
    async checkServiceHealth(service) {
        const startTime = Date.now();

        try {
            const response = await this.makeHttpRequest(service.url, service.timeout);
            const responseTime = Date.now() - startTime;

            let status = 'healthy';
            if (responseTime > service.timeout * 0.8) {
                status = 'degraded';
            }

            return {
                name: service.name,
                status: status,
                responseTime: responseTime,
                statusCode: response.statusCode,
                critical: service.critical,
                lastCheck: new Date().toISOString()
            };
        } catch (error) {
            return {
                name: service.name,
                status: 'unhealthy',
                error: error.message,
                critical: service.critical,
                lastCheck: new Date().toISOString()
            };
        }
    }

    // Faire une requête HTTP avec timeout
    makeHttpRequest(url, timeout) {
        return new Promise((resolve, reject) => {
            const request = (url.startsWith('https') ? https : http).get(url, (response) => {
                resolve(response);
            });

            request.setTimeout(timeout, () => {
                request.abort();
                reject(new Error('Request timeout'));
            });

            request.on('error', reject);
        });
    }

    // Obtenir les métriques système
    async getSystemMetrics() {
        return new Promise((resolve) => {
            exec('top -l 1 | head -n 10', (error, stdout) => {
                if (error) {
                    resolve({
                        cpu: 0,
                        memory: 0,
                        network: 0,
                        response_time: 0
                    });
                    return;
                }

                // Parser les métriques système (simplifié)
                const metrics = {
                    cpu: Math.random() * 100, // Simulation
                    memory: Math.random() * 100,
                    network: Math.random() * 1000,
                    response_time: Math.random() * 500
                };

                resolve(metrics);
            });
        });
    }

    // Déclencher des alertes si nécessaire
    async triggerAlertsIfNeeded(healthReport) {
        // Alertes pour services critiques en panne
        const criticalDown = healthReport.services.filter(s =>
            s.critical && s.status === 'unhealthy'
        );

        if (criticalDown.length > 0) {
            await this.sendAlert('critical', `Services critiques en panne: ${criticalDown.map(s => s.name).join(', ')}`);
        }

        // Alertes pour anomalies détectées
        const criticalAnomalies = healthReport.anomalies.filter(a => a.severity === 'critical');
        if (criticalAnomalies.length > 0) {
            await this.sendAlert('warning', `Anomalies critiques détectées: ${criticalAnomalies.length}`);
        }

        // Alertes prédictives
        for (const anomaly of healthReport.anomalies) {
            if (anomaly.prediction > anomaly.value * 1.5) {
                await this.sendAlert('prediction', `Dégradation prédite pour ${anomaly.metric}: ${anomaly.prediction}`);
            }
        }
    }

    // Envoyer une alerte
    async sendAlert(severity, message) {
        console.log(`🚨 [${severity.toUpperCase()}] ${message}`);

        // Ici, intégration avec le système d'alerting
        // (Prometheus AlertManager, Slack, etc.)
    }

    // Générer un rapport de santé
    generateHealthReport() {
        const recentChecks = this.healthHistory.slice(-10);
        const avgResponseTime = recentChecks.reduce((sum, check) => {
            const serviceAvg = check.services.reduce((s, service) =>
                s + (service.responseTime || 0), 0) / check.services.length;
            return sum + serviceAvg;
        }, 0) / recentChecks.length;

        return {
            timestamp: new Date().toISOString(),
            overallHealth: this.calculateOverallHealth(),
            averageResponseTime: avgResponseTime,
            totalChecks: this.healthHistory.length,
            recentAnomalies: this.anomalyDetector.anomalyHistory.slice(-5),
            recommendations: this.generateHealthRecommendations()
        };
    }

    // Calculer la santé globale
    calculateOverallHealth() {
        if (this.healthHistory.length === 0) return 'unknown';

        const recent = this.healthHistory.slice(-5);
        const criticalIssues = recent.filter(h => h.overallHealth === 'critical').length;
        const degradedIssues = recent.filter(h => h.overallHealth === 'degraded').length;

        if (criticalIssues > 2) return 'critical';
        if (degradedIssues > 3) return 'degraded';
        return 'healthy';
    }

    // Générer des recommandations de santé
    generateHealthRecommendations() {
        const recommendations = [];
        const recentHealth = this.healthHistory.slice(-10);

        // Analyser les patterns de performance
        const slowServices = {};
        recentHealth.forEach(health => {
            health.services.forEach(service => {
                if (service.responseTime > 1000) {
                    slowServices[service.name] = (slowServices[service.name] || 0) + 1;
                }
            });
        });

        for (const [service, count] of Object.entries(slowServices)) {
            if (count > 3) {
                recommendations.push({
                    type: 'performance',
                    service: service,
                    issue: `Service lent détecté ${count} fois`,
                    recommendation: 'Optimiser les performances ou augmenter les ressources'
                });
            }
        }

        return recommendations;
    }
}

module.exports = AdvancedHealthChecker;

// Démarrage automatique si exécuté directement
if (require.main === module) {
    const healthChecker = new AdvancedHealthChecker();
    healthChecker.startContinuousMonitoring();
}
