groups:
  - name: hanuman_predictive_alerts
    interval: 30s
    rules:
      # Prédiction de surcharge CPU
      - alert: CPUOverloadPredicted
        expr: predict_linear(cpu_usage_percent[10m], 300) > 80
        for: 2m
        labels:
          severity: warning
          component: cortex-central
          prediction: true
        annotations:
          summary: "Surcharge CPU prédite dans 5 minutes"
          description: "Le CPU du cortex central devrait atteindre {{ $value }}% dans 5 minutes"
          runbook: "https://hanuman.docs/runbooks/cpu-overload"
          auto_action: "scale_cortex_replicas"

      # Prédiction de fuite mémoire
      - alert: MemoryLeakPredicted
        expr: predict_linear(memory_usage_bytes[15m], 600) > 8e9
        for: 3m
        labels:
          severity: critical
          component: specialized-agents
          prediction: true
        annotations:
          summary: "Fuite mémoire prédite dans 10 minutes"
          description: "La mémoire devrait atteindre {{ $value | humanize }}B dans 10 minutes"
          runbook: "https://hanuman.docs/runbooks/memory-leak"
          auto_action: "restart_leaking_agents"

      # Prédiction de dégradation performance
      - alert: PerformanceDegradationPredicted
        expr: predict_linear(response_time_p95[20m], 900) > 2000
        for: 5m
        labels:
          severity: warning
          component: api-gateway
          prediction: true
        annotations:
          summary: "Dégradation performance prédite dans 15 minutes"
          description: "Le temps de réponse P95 devrait atteindre {{ $value }}ms dans 15 minutes"
          runbook: "https://hanuman.docs/runbooks/performance-degradation"
          auto_action: "optimize_cache_strategy"

      # Détection d'anomalies comportementales
      - alert: BehavioralAnomalyDetected
        expr: |
          (
            abs(rate(http_requests_total[5m]) - rate(http_requests_total[5m] offset 1w)) /
            rate(http_requests_total[5m] offset 1w)
          ) > 0.5
        for: 2m
        labels:
          severity: warning
          component: traffic-analysis
          anomaly: true
        annotations:
          summary: "Anomalie comportementale détectée"
          description: "Le trafic diffère de {{ $value | humanizePercentage }} par rapport à la semaine dernière"
          runbook: "https://hanuman.docs/runbooks/behavioral-anomaly"
          auto_action: "analyze_traffic_pattern"

      # Prédiction de panne agent
      - alert: AgentFailurePredicted
        expr: |
          (
            rate(agent_heartbeat_failures[10m]) > 0.1 and
            predict_linear(agent_heartbeat_failures[10m], 300) > 0.5
          )
        for: 1m
        labels:
          severity: critical
          component: agent-health
          prediction: true
        annotations:
          summary: "Panne d'agent prédite dans 5 minutes"
          description: "L'agent {{ $labels.agent_name }} montre des signes de défaillance"
          runbook: "https://hanuman.docs/runbooks/agent-failure"
          auto_action: "prepare_agent_backup"

      # Détection de surcharge réseau
      - alert: NetworkCongestionPredicted
        expr: predict_linear(network_bytes_total[5m], 180) > 1e9
        for: 2m
        labels:
          severity: warning
          component: network
          prediction: true
        annotations:
          summary: "Congestion réseau prédite dans 3 minutes"
          description: "Le trafic réseau devrait atteindre {{ $value | humanize }}B/s dans 3 minutes"
          runbook: "https://hanuman.docs/runbooks/network-congestion"
          auto_action: "enable_traffic_shaping"

      # Prédiction de saturation disque
      - alert: DiskSaturationPredicted
        expr: predict_linear(disk_usage_percent[30m], 1800) > 90
        for: 5m
        labels:
          severity: critical
          component: storage
          prediction: true
        annotations:
          summary: "Saturation disque prédite dans 30 minutes"
          description: "L'utilisation disque devrait atteindre {{ $value }}% dans 30 minutes"
          runbook: "https://hanuman.docs/runbooks/disk-saturation"
          auto_action: "cleanup_old_logs"

      # Détection d'anomalies de sécurité
      - alert: SecurityAnomalyDetected
        expr: |
          (
            rate(failed_auth_attempts[5m]) > 10 or
            rate(suspicious_requests[5m]) > 5
          )
        for: 1m
        labels:
          severity: critical
          component: security
          anomaly: true
        annotations:
          summary: "Anomalie de sécurité détectée"
          description: "Activité suspecte détectée: {{ $value }} tentatives/requêtes par minute"
          runbook: "https://hanuman.docs/runbooks/security-anomaly"
          auto_action: "enable_enhanced_security"
