# 🚨 HANUMAN - Système d'Alertes Prédictives

## 🎯 Vue d'ensemble

Le système d'alertes prédictives de Hanuman utilise l'intelligence artificielle et l'apprentissage automatique pour détecter les problèmes **avant qu'ils ne se produisent**. Ce système révolutionnaire permet une maintenance prédictive et une auto-correction automatique.

## 🏗️ Architecture

```
hanuman-unified/monitoring/predictive-alerts/
├── rules/                          # Règles d'alerting Prometheus
│   └── predictive-alerts.yml       # Règles prédictives principales
├── scripts/                        # Scripts d'automatisation
│   ├── auto-correction.sh          # Actions d'auto-correction
│   └── deploy-dashboard.sh         # Déploiement dashboard
├── dashboards/                     # Dashboards Grafana
│   └── hanuman-predictive-dashboard.json
├── anomaly-detection.js           # IA de détection d'anomalies
├── health-checks.js               # Health checks avancés
├── alertmanager-config.yml        # Configuration escalation
└── README.md                      # Cette documentation
```

## 🚀 Installation et Configuration

### 1. Configuration Automatique

```bash
# Exécuter le script de configuration
cd hanuman-unified
./scripts/configure-predictive-alerts.sh
```

### 2. Validation de l'Installation

```bash
# Tester la configuration
./scripts/test-predictive-alerts.sh
```

### 3. Déploiement du Dashboard

```bash
# Déployer le dashboard Grafana
cd monitoring/predictive-alerts/scripts
./deploy-dashboard.sh
```

## 🧠 Fonctionnalités Principales

### 1. **Prédiction de Surcharge CPU**
- **Détection**: Prédiction basée sur les tendances des 10 dernières minutes
- **Seuil**: >80% prédit dans 5 minutes
- **Action**: Scale automatique des répliques du cortex central

### 2. **Détection de Fuites Mémoire**
- **Détection**: Analyse des tendances mémoire sur 15 minutes
- **Seuil**: >8GB prédit dans 10 minutes
- **Action**: Redémarrage automatique des agents concernés

### 3. **Prédiction de Dégradation Performance**
- **Détection**: Analyse du temps de réponse P95 sur 20 minutes
- **Seuil**: >2000ms prédit dans 15 minutes
- **Action**: Optimisation automatique de la stratégie de cache

### 4. **Détection d'Anomalies Comportementales**
- **Détection**: Comparaison avec les patterns de la semaine précédente
- **Seuil**: Différence >50% du trafic normal
- **Action**: Analyse automatique des patterns de trafic

### 5. **Prédiction de Pannes d'Agents**
- **Détection**: Analyse des échecs de heartbeat sur 10 minutes
- **Seuil**: >50% d'échecs prédits dans 5 minutes
- **Action**: Préparation automatique d'agents de backup

## 🔧 Auto-Correction

Le système d'auto-correction exécute automatiquement des actions correctives :

### Actions Disponibles

| Action | Description | Déclencheur |
|--------|-------------|-------------|
| `scale_cortex_replicas` | Augmente les répliques du cortex | Surcharge CPU prédite |
| `restart_leaking_agents` | Redémarre les agents avec fuite mémoire | Fuite mémoire détectée |
| `optimize_cache_strategy` | Optimise la stratégie de cache | Dégradation performance |
| `analyze_traffic_pattern` | Analyse les patterns de trafic | Anomalie comportementale |
| `prepare_agent_backup` | Prépare des agents de backup | Panne d'agent prédite |
| `enable_traffic_shaping` | Active le traffic shaping | Congestion réseau |
| `cleanup_old_logs` | Nettoie les anciens logs | Saturation disque |
| `enable_enhanced_security` | Active la sécurité renforcée | Anomalie de sécurité |

### Exécution Manuelle

```bash
# Exécuter une action d'auto-correction manuellement
./scripts/auto-correction.sh "cleanup_old_logs"
```

## 🧠 IA de Détection d'Anomalies

### Utilisation Programmatique

```javascript
const AnomalyDetectionAI = require('./anomaly-detection.js');
const ai = new AnomalyDetectionAI();

// Analyser des métriques
const metrics = {
    cpu: 75,
    memory: 80,
    network: 200,
    response_time: 150
};

const anomalies = await ai.analyzeMetrics(metrics);
console.log('Anomalies détectées:', anomalies);
```

### Fonctionnalités de l'IA

- **Détection basée sur les seuils** : Seuils statiques configurables
- **Apprentissage automatique** : Détection basée sur les patterns historiques
- **Prédiction linéaire** : Prédiction des valeurs futures
- **Recommandations** : Génération automatique de recommandations

## 🏥 Health Checks Avancés

### Démarrage du Monitoring

```javascript
const AdvancedHealthChecker = require('./health-checks.js');
const healthChecker = new AdvancedHealthChecker();

// Démarrer la surveillance continue
healthChecker.startContinuousMonitoring();
```

### Services Surveillés

- **Cortex Central** (Port 3001) - Critique
- **Agents Spécialisés** (Port 3002) - Critique  
- **Système Immunitaire** (Port 3003) - Non critique

### Métriques Collectées

- Temps de réponse des services
- Métriques système (CPU, mémoire, réseau)
- Détection d'anomalies en temps réel
- Historique de santé

## 📢 Escalation Intelligente

### Canaux d'Alerting

| Sévérité | Canal | Délai | Répétition |
|----------|-------|-------|------------|
| **Critical** | Email + Slack + PagerDuty | 0s | 2min |
| **Warning** | Slack | 2min | 1h |
| **Predictive** | Slack (#predictions) | 30s | 10min |
| **Anomaly** | Slack (#anomalies) | 1min | 15min |

### Configuration des Canaux

```yaml
# Variables d'environnement requises
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/...
PAGERDUTY_INTEGRATION_KEY=your-pagerduty-key
```

## 📊 Dashboard de Monitoring

### Accès au Dashboard

- **URL**: http://localhost:3000/d/hanuman-predictive
- **Refresh**: 30 secondes
- **Rétention**: 1 heure par défaut

### Panels Disponibles

1. **🧠 Cortex Central Health** - Statut du cortex central
2. **🤖 Specialized Agents Status** - Nombre d'agents actifs
3. **🔮 Predictive Alerts Timeline** - Timeline des alertes prédictives
4. **💾 Memory Usage Prediction** - Prédiction d'utilisation mémoire
5. **🖥️ CPU Usage Prediction** - Prédiction d'utilisation CPU
6. **🚨 Active Anomalies** - Table des anomalies actives
7. **🔧 Auto-Correction Actions** - Logs des actions d'auto-correction

## 🔍 Monitoring et Logs

### Localisation des Logs

```bash
# Logs d'auto-correction
tail -f hanuman-unified/logs/alerts/auto-correction.log

# Logs de health checks
tail -f hanuman-unified/logs/alerts/health-checks.log

# Rapports de test
ls hanuman-unified/logs/alerts/test-report-*.json
```

### Métriques Prometheus

Les métriques suivantes sont exposées pour Prometheus :

- `cpu_usage_percent` - Utilisation CPU en pourcentage
- `memory_usage_bytes` - Utilisation mémoire en bytes
- `network_bytes_total` - Trafic réseau total
- `response_time_p95` - Temps de réponse P95
- `agent_heartbeat_failures` - Échecs de heartbeat des agents

## 🛠️ Maintenance

### Tests Réguliers

```bash
# Exécuter les tests quotidiennement
./scripts/test-predictive-alerts.sh

# Vérifier les logs d'alerting
grep "ERROR\|CRITICAL" hanuman-unified/logs/alerts/*.log
```

### Calibrage des Seuils

Les seuils peuvent être ajustés dans :

- `rules/predictive-alerts.yml` - Seuils Prometheus
- `anomaly-detection.js` - Seuils IA
- `alertmanager-config.yml` - Configuration escalation

### Mise à Jour

```bash
# Reconfigurer après modifications
./scripts/configure-predictive-alerts.sh

# Redéployer le dashboard
cd scripts && ./deploy-dashboard.sh
```

## 🎯 Roadmap Excellence 10/10

Ce système d'alertes prédictives fait partie du **Jour 3** du roadmap Excellence 10/10 :

- ✅ **Alerting Prédictif** - Détection avant occurrence
- ✅ **Auto-correction** - Actions automatiques
- ✅ **IA d'Anomalies** - Apprentissage automatique
- ✅ **Escalation Intelligente** - Routage optimal
- ✅ **Dashboard Avancé** - Visualisation temps réel

## 📞 Support

Pour toute question ou problème :

1. Consulter les logs dans `hanuman-unified/logs/alerts/`
2. Exécuter `./scripts/test-predictive-alerts.sh`
3. Vérifier la configuration dans `monitoring/predictive-alerts/`

---

**🕉️ Hanuman protège et surveille Retreat And Be avec une intelligence prédictive avancée.**
