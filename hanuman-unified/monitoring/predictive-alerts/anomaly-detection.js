/**
 * 🧠 HANUMAN - IA de Détection d'Anomalies
 * ========================================
 * Système d'IA pour détecter les anomalies comportementales
 */

const fs = require('fs');
const path = require('path');

class AnomalyDetectionAI {
    constructor() {
        this.models = new Map();
        this.thresholds = {
            cpu: { normal: [20, 60], warning: 70, critical: 85 },
            memory: { normal: [30, 70], warning: 80, critical: 90 },
            network: { normal: [0, 100], warning: 500, critical: 1000 },
            response_time: { normal: [50, 200], warning: 500, critical: 1000 }
        };
        this.learningData = [];
        this.anomalyHistory = [];
    }

    // Analyser les métriques en temps réel
    async analyzeMetrics(metrics) {
        const anomalies = [];

        for (const [metricName, value] of Object.entries(metrics)) {
            const anomaly = await this.detectAnomaly(metricName, value);
            if (anomaly) {
                anomalies.push(anomaly);
            }
        }

        return anomalies;
    }

    // Détecter une anomalie pour une métrique spécifique
    async detectAnomaly(metricName, value) {
        const threshold = this.thresholds[metricName];
        if (!threshold) return null;

        // Détection basée sur les seuils statiques
        let severity = 'normal';
        if (value > threshold.critical) {
            severity = 'critical';
        } else if (value > threshold.warning) {
            severity = 'warning';
        } else if (value < threshold.normal[0] || value > threshold.normal[1]) {
            severity = 'info';
        }

        // Détection basée sur l'apprentissage automatique
        const mlAnomaly = await this.detectMLAnomaly(metricName, value);

        if (severity !== 'normal' || mlAnomaly) {
            const anomaly = {
                metric: metricName,
                value: value,
                severity: severity,
                timestamp: new Date().toISOString(),
                mlDetected: mlAnomaly,
                prediction: await this.predictFutureValue(metricName, value)
            };

            this.anomalyHistory.push(anomaly);
            return anomaly;
        }

        return null;
    }

    // Détection ML basée sur les patterns historiques
    async detectMLAnomaly(metricName, value) {
        const historicalData = this.getHistoricalData(metricName);
        if (historicalData.length < 10) return false;

        // Calcul de la moyenne et écart-type
        const mean = historicalData.reduce((a, b) => a + b, 0) / historicalData.length;
        const variance = historicalData.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / historicalData.length;
        const stdDev = Math.sqrt(variance);

        // Détection d'anomalie si la valeur est à plus de 2 écarts-types
        const zScore = Math.abs((value - mean) / stdDev);
        return zScore > 2;
    }

    // Prédire la valeur future d'une métrique
    async predictFutureValue(metricName, currentValue) {
        const historicalData = this.getHistoricalData(metricName);
        if (historicalData.length < 5) return currentValue;

        // Régression linéaire simple pour prédiction
        const n = historicalData.length;
        const x = Array.from({length: n}, (_, i) => i);
        const y = historicalData;

        const sumX = x.reduce((a, b) => a + b, 0);
        const sumY = y.reduce((a, b) => a + b, 0);
        const sumXY = x.reduce((acc, xi, i) => acc + xi * y[i], 0);
        const sumXX = x.reduce((acc, xi) => acc + xi * xi, 0);

        const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
        const intercept = (sumY - slope * sumX) / n;

        // Prédiction pour les 5 prochaines minutes
        const futureX = n + 5;
        return slope * futureX + intercept;
    }

    // Obtenir les données historiques pour une métrique
    getHistoricalData(metricName) {
        return this.learningData
            .filter(data => data.metric === metricName)
            .map(data => data.value)
            .slice(-50); // Garder les 50 dernières valeurs
    }

    // Ajouter des données d'apprentissage
    addLearningData(metricName, value) {
        this.learningData.push({
            metric: metricName,
            value: value,
            timestamp: new Date().toISOString()
        });

        // Garder seulement les 1000 dernières entrées
        if (this.learningData.length > 1000) {
            this.learningData = this.learningData.slice(-1000);
        }
    }

    // Générer un rapport d'anomalies
    generateAnomalyReport() {
        const report = {
            timestamp: new Date().toISOString(),
            totalAnomalies: this.anomalyHistory.length,
            criticalAnomalies: this.anomalyHistory.filter(a => a.severity === 'critical').length,
            warningAnomalies: this.anomalyHistory.filter(a => a.severity === 'warning').length,
            recentAnomalies: this.anomalyHistory.slice(-10),
            recommendations: this.generateRecommendations()
        };

        return report;
    }

    // Générer des recommandations basées sur les anomalies
    generateRecommendations() {
        const recommendations = [];
        const recentAnomalies = this.anomalyHistory.slice(-20);

        // Analyser les patterns d'anomalies
        const metricCounts = {};
        recentAnomalies.forEach(anomaly => {
            metricCounts[anomaly.metric] = (metricCounts[anomaly.metric] || 0) + 1;
        });

        // Générer des recommandations
        for (const [metric, count] of Object.entries(metricCounts)) {
            if (count > 5) {
                recommendations.push({
                    metric: metric,
                    issue: `Anomalies fréquentes détectées (${count} dans les 20 dernières)`,
                    recommendation: this.getRecommendationForMetric(metric),
                    priority: count > 10 ? 'high' : 'medium'
                });
            }
        }

        return recommendations;
    }

    // Obtenir une recommandation pour une métrique spécifique
    getRecommendationForMetric(metric) {
        const recommendations = {
            cpu: "Considérer l'ajout de ressources CPU ou l'optimisation du code",
            memory: "Vérifier les fuites mémoire et optimiser l'utilisation",
            network: "Analyser le trafic réseau et considérer la mise en cache",
            response_time: "Optimiser les requêtes et améliorer les performances"
        };

        return recommendations[metric] || "Analyser la métrique pour identifier la cause";
    }
}

module.exports = AnomalyDetectionAI;
