const AdvancedHealthChecker = require('./health-checks.js');

console.log('🏥 Démarrage des health checks avancé<PERSON>...');

const healthChecker = new AdvancedHealthChecker();

// Démarrer la surveillance continue
healthChecker.startContinuousMonitoring();

// Générer un rapport de santé toutes les 10 minutes
setInterval(() => {
    const report = healthChecker.generateHealthReport();
    console.log('📊 Rapport de santé:', JSON.stringify(report, null, 2));
}, 600000); // 10 minutes

console.log('✅ Health checks avancés actifs');
