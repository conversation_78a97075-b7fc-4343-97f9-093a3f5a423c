{"dashboard": {"id": null, "title": "🕉️ Hanuman - Predictive Monitoring Dashboard", "tags": ["hanuman", "predictive", "ai"], "timezone": "browser", "panels": [{"id": 1, "title": "🧠 Cortex Central Health", "type": "stat", "targets": [{"expr": "up{job=\"cortex-central\"}", "legendFormat": "Cortex Status"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "🤖 Specialized Agents Status", "type": "stat", "targets": [{"expr": "count(up{job=~\"agent-.*\"} == 1)", "legendFormat": "Active Agents"}], "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}}, {"id": 3, "title": "🔮 Predictive Alerts Timeline", "type": "timeseries", "targets": [{"expr": "increase(prometheus_notifications_total{instance=\"alertmanager\"}[5m])", "legendFormat": "Predictive Alerts"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 4, "title": "💾 Memory Usage Prediction", "type": "timeseries", "targets": [{"expr": "predict_linear(memory_usage_bytes[15m], 600)", "legendFormat": "Predicted Memory (10min)"}, {"expr": "memory_usage_bytes", "legendFormat": "Current Memory"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 5, "title": "🖥️ CPU Usage Prediction", "type": "timeseries", "targets": [{"expr": "predict_linear(cpu_usage_percent[10m], 300)", "legendFormat": "Predicted CPU (5min)"}, {"expr": "cpu_usage_percent", "legendFormat": "Current CPU"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 6, "title": "🚨 Active Anomalies", "type": "table", "targets": [{"expr": "ALERTS{alertstate=\"firing\", anomaly=\"true\"}", "format": "table"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}}, {"id": 7, "title": "🔧 Auto-Correction Actions", "type": "logs", "targets": [{"expr": "{job=\"hanuman-auto-correction\"}", "legendFormat": "Auto-Correction Logs"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 24}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "30s"}}