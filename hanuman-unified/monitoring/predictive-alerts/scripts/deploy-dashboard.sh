#!/bin/bash

# 📊 Déploiement du Dashboard Hanuman
# ==================================

GRAFANA_URL="${GRAFANA_URL:-http://localhost:3000}"
GRAFANA_API_KEY="${GRAFANA_API_KEY:-admin:admin}"

echo "📊 Déploiement du dashboard Hanuman..."

# Créer le dashboard via l'API Grafana
curl -X POST \
  "$GRAFANA_URL/api/dashboards/db" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $GRAFANA_API_KEY" \
  -d @hanuman-predictive-dashboard.json

echo "✅ Dashboard déployé avec succès!"
