const AnomalyDetectionAI = require('./anomaly-detection.js');

console.log('🧠 Démarrage de l\'IA de détection d\'anomalies Hanuman...');

const ai = new AnomalyDetectionAI();

// Simuler des métriques en continu
setInterval(async () => {
    // Obtenir des métriques simulées (en production, connecter aux vraies métriques)
    const metrics = {
        cpu: Math.random() * 100,
        memory: Math.random() * 100,
        network: Math.random() * 1000,
        response_time: Math.random() * 500
    };
    
    // Ajouter aux données d'apprentissage
    for (const [metric, value] of Object.entries(metrics)) {
        ai.addLearningData(metric, value);
    }
    
    // Analyser les anomalies
    const anomalies = await ai.analyzeMetrics(metrics);
    
    if (anomalies.length > 0) {
        console.log(`🚨 ${anomalies.length} anomalie(s) détectée(s):`, anomalies);
        
        // Déclencher des actions d'auto-correction si nécessaire
        for (const anomaly of anomalies) {
            if (anomaly.severity === 'critical') {
                console.log(`🔧 Déclenchement auto-correction pour ${anomaly.metric}`);
                // Ici, déclencher l'auto-correction
            }
        }
    }
    
    // Générer un rapport toutes les 5 minutes
    if (Date.now() % 300000 < 5000) {
        const report = ai.generateAnomalyReport();
        console.log('📊 Rapport d\'anomalies:', report);
    }
    
}, 5000); // Toutes les 5 secondes

console.log('✅ IA de détection d\'anomalies active');
