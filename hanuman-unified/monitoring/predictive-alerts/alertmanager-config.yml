global:
  resolve_timeout: 5m
  slack_api_url: '${SLACK_WEBHOOK_URL}'

route:
  group_by: ['alertname', 'component', 'severity']
  group_wait: 10s
  group_interval: 30s
  repeat_interval: 5m
  receiver: 'hanuman-default'
  routes:
    # Alertes critiques - escalation immédiate
    - match:
        severity: critical
      receiver: 'hanuman-critical'
      group_wait: 0s
      repeat_interval: 2m
      routes:
        # Sécurité - escalation spéciale
        - match:
            component: security
          receiver: 'hanuman-security-team'
          group_wait: 0s
          repeat_interval: 1m

        # Cortex Central - escalation DevOps
        - match:
            component: cortex-central
          receiver: 'hanuman-devops-team'
          group_wait: 5s
          repeat_interval: 2m

    # Alertes prédictives - escalation préventive
    - match:
        prediction: "true"
      receiver: 'hanuman-predictive'
      group_wait: 30s
      repeat_interval: 10m

    # Anomalies comportementales - escalation analytique
    - match:
        anomaly: "true"
      receiver: 'hanuman-anomaly-team'
      group_wait: 1m
      repeat_interval: 15m

receivers:
  - name: 'hanuman-default'
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#hanuman-alerts'
        title: '🕉️ Hanuman Alert'
        text: |
          {{ range .Alerts }}
          *{{ .Annotations.summary }}*
          Component: {{ .Labels.component }}
          Severity: {{ .Labels.severity }}
          {{ .Annotations.description }}
          {{ end }}

  - name: 'hanuman-critical'
    email_configs:
      - to: '<EMAIL>'
        subject: '🚨 [CRITICAL] Hanuman Alert'
        body: |
          🚨 ALERTE CRITIQUE HANUMAN 🚨

          {{ range .Alerts }}
          Component: {{ .Labels.component }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Auto-Action: {{ .Annotations.auto_action }}
          Runbook: {{ .Annotations.runbook }}
          {{ end }}
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#hanuman-critical'
        title: '🚨 CRITICAL: Hanuman System Alert'
        text: |
          {{ range .Alerts }}
          🚨 *{{ .Annotations.summary }}*
          Component: {{ .Labels.component }}
          {{ .Annotations.description }}
          Auto-correction: {{ .Annotations.auto_action }}
          {{ end }}

  - name: 'hanuman-security-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '🛡️ [SECURITY] Hanuman Security Alert'
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#security-alerts'
        title: '🛡️ Security Alert - Hanuman'

  - name: 'hanuman-devops-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '⚙️ [DEVOPS] Hanuman Infrastructure Alert'
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#devops-alerts'
        title: '⚙️ DevOps Alert - Hanuman'

  - name: 'hanuman-predictive'
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#hanuman-predictions'
        title: '🔮 Predictive Alert - Hanuman'
        text: |
          {{ range .Alerts }}
          🔮 *Prédiction: {{ .Annotations.summary }}*
          {{ .Annotations.description }}
          Action préventive: {{ .Annotations.auto_action }}
          {{ end }}

  - name: 'hanuman-anomaly-team'
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#hanuman-anomalies'
        title: '🧠 Anomaly Detected - Hanuman'
        text: |
          {{ range .Alerts }}
          🧠 *Anomalie: {{ .Annotations.summary }}*
          {{ .Annotations.description }}
          Analyse requise: {{ .Annotations.auto_action }}
          {{ end }}

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'component']
