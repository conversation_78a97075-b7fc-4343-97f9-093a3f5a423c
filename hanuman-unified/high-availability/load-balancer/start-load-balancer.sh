#!/bin/bash

echo "⚖️ Démarrage du load balancer Hanuman..."

# Vérifier si nginx est installé
if command -v nginx &> /dev/null; then
    echo "✅ Nginx détecté - Configuration du load balancer"
    
    # Copier la configuration
    sudo cp nginx.conf /etc/nginx/sites-available/hanuman-cortex
    sudo ln -sf /etc/nginx/sites-available/hanuman-cortex /etc/nginx/sites-enabled/
    
    # Tester la configuration
    sudo nginx -t
    
    # Redémarrer nginx
    sudo systemctl reload nginx
    
    echo "✅ Load balancer nginx configuré et démarré"
else
    echo "⚠️  Nginx non installé - Utilisation du load balancer Node.js"
    
    # Démarrer le load balancer Node.js
    node ../load-balancer-node.js &
    echo $! > load-balancer.pid
    
    echo "✅ Load balancer Node.js démarré"
fi
