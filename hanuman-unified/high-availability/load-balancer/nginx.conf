# Configuration Nginx Load Balancer pour Hanuman Cortex Central
upstream hanuman_cortex {
    # Répartition de charge avec health checks
    server localhost:3001 max_fails=3 fail_timeout=30s;
    server localhost:3011 max_fails=3 fail_timeout=30s backup;
    server localhost:3021 max_fails=3 fail_timeout=30s backup;
}

server {
    listen 3000;
    server_name hanuman-cortex;
    
    # Health check endpoint
    location /health {
        proxy_pass http://hanuman_cortex;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_connect_timeout 5s;
        proxy_read_timeout 10s;
    }
    
    # Proxy vers le cortex actif
    location / {
        proxy_pass http://hanuman_cortex;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_connect_timeout 5s;
        proxy_read_timeout 30s;
        
        # Retry sur les autres répliques en cas d'échec
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503;
        proxy_next_upstream_tries 3;
        proxy_next_upstream_timeout 30s;
    }
    
    # Logs pour monitoring
    access_log /var/log/nginx/hanuman-cortex-access.log;
    error_log /var/log/nginx/hanuman-cortex-error.log;
}
