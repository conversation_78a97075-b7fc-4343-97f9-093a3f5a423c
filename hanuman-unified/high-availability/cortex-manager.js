/**
 * 🧠 HANUMAN CORTEX MANAGER - EXCELLENCE 10/10
 * =============================================
 * Gestionnaire principal du Cortex Central multi-instance
 * Jour 4: Redondance & Backup Système
 */

const { EventEmitter } = require('events');
const axios = require('axios');

class CortexManager extends EventEmitter {
    constructor(config = {}) {
        super();
        
        this.config = {
            replicas: config.replicas || 3,
            healthCheckInterval: config.healthCheckInterval || 5000,
            failoverTimeout: config.failoverTimeout || 30000,
            ports: config.ports || [3001, 3002, 3003],
            ...config
        };
        
        this.replicas = [];
        this.activeReplica = 0;
        this.isRunning = false;
        this.healthCheckTimer = null;
        
        this.initializeReplicas();
    }

    /**
     * Initialiser les répliques du Cortex Central
     */
    initializeReplicas() {
        console.log('🧠 Initialisation des répliques Cortex Central...');
        
        for (let i = 0; i < this.config.replicas; i++) {
            this.replicas.push({
                id: i,
                port: this.config.ports[i],
                status: i === 0 ? 'active' : 'standby',
                healthEndpoint: `http://localhost:${this.config.ports[i]}/health`,
                lastHealthCheck: null,
                failureCount: 0,
                worker: null
            });
        }
        
        console.log(`✅ ${this.replicas.length} répliques Cortex initialisées`);
    }

    /**
     * Démarrer le gestionnaire de répliques
     */
    async start() {
        console.log('🚀 Démarrage du gestionnaire Cortex...');
        
        this.isRunning = true;
        
        // Démarrer les répliques
        await this.startReplicas();
        
        // Démarrer la surveillance de santé
        this.startHealthMonitoring();
        
        console.log('✅ Gestionnaire Cortex démarré avec succès');
        this.emit('started');
    }

    /**
     * Démarrer toutes les répliques
     */
    async startReplicas() {
        console.log('🔄 Démarrage des répliques...');
        
        for (const replica of this.replicas) {
            try {
                await this.startReplica(replica);
                console.log(`✅ Réplique Cortex-${replica.id} démarrée (port ${replica.port})`);
            } catch (error) {
                console.error(`❌ Erreur démarrage Cortex-${replica.id}:`, error.message);
            }
        }
    }

    /**
     * Démarrer une réplique spécifique
     */
    async startReplica(replica) {
        // Simuler le démarrage d'une réplique
        // En production, ceci démarrerait un processus Node.js ou un container
        replica.worker = {
            pid: Math.floor(Math.random() * 10000) + 1000,
            startTime: Date.now(),
            kill: () => {
                replica.status = 'failed';
                replica.worker = null;
            }
        };
        
        replica.status = replica.id === this.activeReplica ? 'active' : 'standby';
        
        return replica.worker;
    }

    /**
     * Surveillance de santé des répliques
     */
    startHealthMonitoring() {
        this.healthCheckTimer = setInterval(() => {
            this.checkReplicasHealth();
        }, this.config.healthCheckInterval);
        
        console.log('🏥 Surveillance de santé des répliques activée');
    }

    /**
     * Vérifier la santé de toutes les répliques
     */
    async checkReplicasHealth() {
        if (!this.isRunning) return;
        
        const now = Date.now();
        
        for (let i = 0; i < this.replicas.length; i++) {
            const replica = this.replicas[i];
            if (!replica) continue;
            
            // Vérifier si la réplique répond
            const isHealthy = await this.checkReplicaHealth(replica);
            replica.lastHealthCheck = now;
            
            if (!isHealthy) {
                replica.failureCount++;
                console.log(`⚠️  Cortex-${i} en panne (échecs: ${replica.failureCount})`);
                
                // Si c'est la réplique active qui tombe en panne
                if (i === this.activeReplica && replica.failureCount >= 2) {
                    console.log(`🚨 Réplique active Cortex-${i} en panne - Déclenchement failover`);
                    await this.performFailover();
                }
            } else {
                replica.failureCount = 0;
            }
        }
    }

    /**
     * Vérifier la santé d'une réplique
     */
    async checkReplicaHealth(replica) {
        try {
            // Simuler un health check
            // En production, ceci ferait un appel HTTP réel
            if (replica.worker && replica.status !== 'failed') {
                // Simuler 95% de succès
                return Math.random() > 0.05;
            }
            return false;
        } catch (error) {
            return false;
        }
    }

    /**
     * Effectuer un failover automatique
     */
    async performFailover() {
        console.log('🔄 Démarrage du failover automatique...');
        
        const startTime = Date.now();
        
        // Trouver une réplique saine
        for (let i = 0; i < this.replicas.length; i++) {
            if (i === this.activeReplica) continue;
            
            const replica = this.replicas[i];
            if (replica && await this.checkReplicaHealth(replica)) {
                // Basculer vers cette réplique
                const oldActive = this.activeReplica;
                this.activeReplica = i;
                replica.status = 'active';
                
                // Marquer l'ancienne comme failed
                if (this.replicas[oldActive]) {
                    this.replicas[oldActive].status = 'failed';
                }
                
                const failoverTime = Date.now() - startTime;
                
                console.log(`✅ Failover réussi: Cortex-${oldActive} → Cortex-${i}`);
                console.log(`⏱️  Temps de failover: ${failoverTime}ms`);
                
                // Notifier le load balancer du changement
                await this.notifyLoadBalancer();
                
                // Tenter de redémarrer l'ancienne réplique
                this.restartReplica(oldActive);
                
                this.emit('failover', {
                    from: oldActive,
                    to: i,
                    time: failoverTime
                });
                
                return true;
            }
        }
        
        console.error('❌ Failover échoué - Aucune réplique saine disponible');
        this.emit('failover-failed');
        return false;
    }

    /**
     * Notifier le load balancer du changement
     */
    async notifyLoadBalancer() {
        console.log('🔄 Notification du load balancer...');
        
        // Simuler la mise à jour de la configuration nginx
        // En production, ceci mettrait à jour la config nginx et rechargerait
        
        const activeReplica = this.replicas[this.activeReplica];
        console.log(`📡 Load balancer mis à jour: port ${activeReplica.port}`);
    }

    /**
     * Redémarrer une réplique
     */
    async restartReplica(replicaId) {
        console.log(`🔄 Redémarrage de Cortex-${replicaId}...`);
        
        const replica = this.replicas[replicaId];
        if (!replica) return;
        
        try {
            // Attendre un peu avant de redémarrer
            await new Promise(resolve => setTimeout(resolve, 5000));
            
            await this.startReplica(replica);
            replica.status = 'standby';
            replica.failureCount = 0;
            
            console.log(`✅ Cortex-${replicaId} redémarré avec succès`);
        } catch (error) {
            console.error(`❌ Erreur redémarrage Cortex-${replicaId}:`, error.message);
        }
    }

    /**
     * Obtenir le statut du gestionnaire
     */
    getStatus() {
        return {
            isRunning: this.isRunning,
            activeReplica: this.activeReplica,
            replicas: this.replicas.map(r => ({
                id: r.id,
                port: r.port,
                status: r.status,
                failureCount: r.failureCount,
                lastHealthCheck: r.lastHealthCheck
            })),
            config: this.config
        };
    }

    /**
     * Arrêter le gestionnaire
     */
    async stop() {
        console.log('🛑 Arrêt du gestionnaire Cortex...');
        
        this.isRunning = false;
        
        if (this.healthCheckTimer) {
            clearInterval(this.healthCheckTimer);
            this.healthCheckTimer = null;
        }
        
        // Arrêter toutes les répliques
        for (const replica of this.replicas) {
            if (replica.worker) {
                replica.worker.kill();
                replica.worker = null;
            }
            replica.status = 'stopped';
        }
        
        console.log('✅ Gestionnaire Cortex arrêté');
        this.emit('stopped');
    }
}

module.exports = CortexManager;

// Export pour les tests
if (require.main === module) {
    // Mode test/démo
    const manager = new CortexManager();
    
    manager.on('started', () => {
        console.log('🎉 Gestionnaire Cortex démarré');
    });
    
    manager.on('failover', (event) => {
        console.log(`🔄 Failover détecté: ${event.from} → ${event.to} (${event.time}ms)`);
    });
    
    manager.start().catch(console.error);
    
    // Arrêt propre
    process.on('SIGINT', async () => {
        await manager.stop();
        process.exit(0);
    });
}
