/**
 * 🧠 HANUMAN - Configuration Cortex Central Multi-Instance
 * =======================================================
 * Gestion de 3 répliques avec failover automatique
 */

const cluster = require('cluster');
const os = require('os');

class CortexReplicaManager {
    constructor() {
        this.replicas = [];
        this.activeReplica = null;
        this.replicaCount = 3;
        this.ports = [3001, 3011, 3021]; // Ports pour les 3 répliques
        this.healthCheckInterval = 5000; // 5 secondes
        this.failoverTimeout = 30000; // 30 secondes max pour failover
    }

    // Démarrer toutes les répliques
    async startReplicas() {
        console.log('🧠 Démarrage des répliques Cortex Central...');
        
        for (let i = 0; i < this.replicaCount; i++) {
            await this.startReplica(i);
        }
        
        // Définir la première réplique comme active
        this.activeReplica = 0;
        console.log(`✅ Réplique active: Cortex-${this.activeReplica} (Port: ${this.ports[this.activeReplica]})`);
        
        // Démarrer la surveillance
        this.startHealthMonitoring();
    }

    // Démarrer une réplique spécifique
    async startReplica(replicaId) {
        const port = this.ports[replicaId];
        
        if (cluster.isMaster) {
            const worker = cluster.fork({
                REPLICA_ID: replicaId,
                REPLICA_PORT: port,
                REPLICA_ROLE: replicaId === 0 ? 'active' : 'passive'
            });
            
            this.replicas[replicaId] = {
                id: replicaId,
                port: port,
                worker: worker,
                status: 'starting',
                lastHealthCheck: Date.now()
            };
            
            worker.on('message', (msg) => {
                if (msg.type === 'health') {
                    this.replicas[replicaId].status = msg.status;
                    this.replicas[replicaId].lastHealthCheck = Date.now();
                }
            });
            
            console.log(`🚀 Réplique Cortex-${replicaId} démarrée sur port ${port}`);
        }
    }

    // Surveillance de santé des répliques
    startHealthMonitoring() {
        setInterval(() => {
            this.checkReplicasHealth();
        }, this.healthCheckInterval);
        
        console.log('🏥 Surveillance de santé des répliques activée');
    }

    // Vérifier la santé de toutes les répliques
    async checkReplicasHealth() {
        const now = Date.now();
        
        for (let i = 0; i < this.replicas.length; i++) {
            const replica = this.replicas[i];
            if (!replica) continue;
            
            // Vérifier si la réplique répond
            const isHealthy = await this.checkReplicaHealth(replica);
            
            if (!isHealthy && i === this.activeReplica) {
                console.log(`🚨 Réplique active Cortex-${i} en panne - Déclenchement failover`);
                await this.performFailover();
            }
        }
    }

    // Vérifier la santé d'une réplique
    async checkReplicaHealth(replica) {
        try {
            const response = await fetch(`http://localhost:${replica.port}/health`);
            return response.ok;
        } catch (error) {
            return false;
        }
    }

    // Effectuer un failover vers une réplique saine
    async performFailover() {
        const startTime = Date.now();
        console.log('🔄 Démarrage du processus de failover...');
        
        // Trouver une réplique saine
        for (let i = 0; i < this.replicas.length; i++) {
            if (i === this.activeReplica) continue;
            
            const replica = this.replicas[i];
            if (replica && await this.checkReplicaHealth(replica)) {
                // Basculer vers cette réplique
                const oldActive = this.activeReplica;
                this.activeReplica = i;
                
                console.log(`✅ Failover réussi: Cortex-${oldActive} → Cortex-${i}`);
                console.log(`⏱️  Temps de failover: ${Date.now() - startTime}ms`);
                
                // Notifier le load balancer du changement
                await this.notifyLoadBalancer();
                
                // Tenter de redémarrer l'ancienne réplique
                this.restartReplica(oldActive);
                
                return true;
            }
        }
        
        console.error('❌ Failover échoué - Aucune réplique saine disponible');
        return false;
    }

    // Notifier le load balancer du changement d'active
    async notifyLoadBalancer() {
        try {
            // Mettre à jour la configuration du load balancer
            const activePort = this.ports[this.activeReplica];
            console.log(`📡 Notification load balancer: nouvelle réplique active sur port ${activePort}`);
            
            // Ici, intégration avec nginx/haproxy pour rediriger le trafic
            
        } catch (error) {
            console.error('❌ Erreur notification load balancer:', error.message);
        }
    }

    // Redémarrer une réplique défaillante
    async restartReplica(replicaId) {
        console.log(`🔄 Redémarrage de la réplique Cortex-${replicaId}...`);
        
        try {
            // Arrêter l'ancienne instance
            if (this.replicas[replicaId] && this.replicas[replicaId].worker) {
                this.replicas[replicaId].worker.kill();
            }
            
            // Attendre un peu avant de redémarrer
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // Redémarrer la réplique
            await this.startReplica(replicaId);
            
            console.log(`✅ Réplique Cortex-${replicaId} redémarrée avec succès`);
        } catch (error) {
            console.error(`❌ Erreur redémarrage réplique ${replicaId}:`, error.message);
        }
    }

    // Obtenir le statut de toutes les répliques
    getReplicasStatus() {
        return this.replicas.map((replica, index) => ({
            id: index,
            port: replica ? replica.port : 'N/A',
            status: replica ? replica.status : 'stopped',
            isActive: index === this.activeReplica,
            lastHealthCheck: replica ? new Date(replica.lastHealthCheck).toISOString() : 'N/A'
        }));
    }
}

// Démarrage automatique si exécuté directement
if (require.main === module) {
    const manager = new CortexReplicaManager();
    
    if (cluster.isMaster) {
        manager.startReplicas();
        
        // Afficher le statut toutes les 30 secondes
        setInterval(() => {
            console.log('📊 Statut des répliques:', manager.getReplicasStatus());
        }, 30000);
        
    } else {
        // Code pour les workers (répliques individuelles)
        const replicaId = process.env.REPLICA_ID;
        const port = process.env.REPLICA_PORT;
        const role = process.env.REPLICA_ROLE;
        
        console.log(`🧠 Cortex-${replicaId} démarré en mode ${role} sur port ${port}`);
        
        // Simuler un serveur cortex (en production, utiliser le vrai cortex)
        const http = require('http');
        const server = http.createServer((req, res) => {
            if (req.url === '/health') {
                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ 
                    status: 'healthy', 
                    replica: replicaId, 
                    role: role,
                    timestamp: new Date().toISOString()
                }));
            } else {
                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ 
                    message: `Hanuman Cortex Central - Réplique ${replicaId}`,
                    role: role,
                    timestamp: new Date().toISOString()
                }));
            }
        });
        
        server.listen(port, () => {
            console.log(`✅ Cortex-${replicaId} écoute sur port ${port}`);
            
            // Envoyer des signaux de santé au master
            setInterval(() => {
                process.send({ 
                    type: 'health', 
                    status: 'healthy',
                    replica: replicaId 
                });
            }, 5000);
        });
    }
}

module.exports = CortexReplicaManager;
