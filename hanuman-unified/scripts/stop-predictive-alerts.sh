#!/bin/bash

# 🛑 HANUMAN - Arrêt des Alertes Prédictives
# ==========================================
# Script pour arrêter le système d'alerting prédictif

set -e

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration des chemins
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
HANUMAN_ROOT="$PROJECT_ROOT/hanuman-unified"

echo -e "${PURPLE}🛑 HANUMAN - Arrêt Alertes Prédictives${NC}"
echo -e "${PURPLE}=====================================${NC}"
echo ""

# Fonction de logging
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}" >&2
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# Arrêter l'IA de détection d'anomalies
stop_anomaly_detection() {
    log "🧠 Arrêt de l'IA de détection d'anomalies..."
    
    local pid_file="$HANUMAN_ROOT/logs/alerts/anomaly-ai.pid"
    
    if [[ -f "$pid_file" ]]; then
        local pid=$(cat "$pid_file")
        if ps -p $pid > /dev/null 2>&1; then
            kill $pid
            sleep 2
            if ps -p $pid > /dev/null 2>&1; then
                warning "Processus résistant, force kill..."
                kill -9 $pid
            fi
            rm -f "$pid_file"
            success "IA de détection d'anomalies arrêtée"
        else
            warning "Processus IA déjà arrêté"
            rm -f "$pid_file"
        fi
    else
        warning "Fichier PID de l'IA non trouvé"
    fi
}

# Arrêter les health checks
stop_health_checks() {
    log "🏥 Arrêt des health checks avancés..."
    
    local pid_file="$HANUMAN_ROOT/logs/alerts/health-checks.pid"
    
    if [[ -f "$pid_file" ]]; then
        local pid=$(cat "$pid_file")
        if ps -p $pid > /dev/null 2>&1; then
            kill $pid
            sleep 2
            if ps -p $pid > /dev/null 2>&1; then
                warning "Processus résistant, force kill..."
                kill -9 $pid
            fi
            rm -f "$pid_file"
            success "Health checks avancés arrêtés"
        else
            warning "Processus health checks déjà arrêté"
            rm -f "$pid_file"
        fi
    else
        warning "Fichier PID des health checks non trouvé"
    fi
}

# Nettoyer les processus orphelins
cleanup_orphan_processes() {
    log "🧹 Nettoyage des processus orphelins..."
    
    # Chercher les processus Node.js liés aux alertes prédictives
    local orphan_pids=$(pgrep -f "anomaly-detection\|health-checks\|start-anomaly-ai\|start-health-checks" || true)
    
    if [[ -n "$orphan_pids" ]]; then
        warning "Processus orphelins détectés: $orphan_pids"
        echo "$orphan_pids" | xargs kill -9 2>/dev/null || true
        success "Processus orphelins nettoyés"
    else
        success "Aucun processus orphelin détecté"
    fi
}

# Afficher le statut final
show_final_status() {
    log "📊 Vérification de l'arrêt..."
    
    echo ""
    echo -e "${CYAN}🕉️  HANUMAN - Statut après arrêt${NC}"
    echo -e "${CYAN}===============================${NC}"
    echo ""
    
    # Vérifier que les processus sont bien arrêtés
    local ai_running=false
    local health_running=false
    
    if [[ -f "$HANUMAN_ROOT/logs/alerts/anomaly-ai.pid" ]]; then
        local ai_pid=$(cat "$HANUMAN_ROOT/logs/alerts/anomaly-ai.pid")
        if ps -p $ai_pid > /dev/null 2>&1; then
            ai_running=true
        fi
    fi
    
    if [[ -f "$HANUMAN_ROOT/logs/alerts/health-checks.pid" ]]; then
        local health_pid=$(cat "$HANUMAN_ROOT/logs/alerts/health-checks.pid")
        if ps -p $health_pid > /dev/null 2>&1; then
            health_running=true
        fi
    fi
    
    if $ai_running; then
        echo -e "${RED}🧠 IA Détection Anomalies: ❌ ENCORE ACTIF${NC}"
    else
        echo -e "${GREEN}🧠 IA Détection Anomalies: ✅ ARRÊTÉ${NC}"
    fi
    
    if $health_running; then
        echo -e "${RED}🏥 Health Checks: ❌ ENCORE ACTIF${NC}"
    else
        echo -e "${GREEN}🏥 Health Checks: ✅ ARRÊTÉ${NC}"
    fi
    
    echo ""
    
    if ! $ai_running && ! $health_running; then
        echo -e "${GREEN}🎉 Tous les services d'alertes prédictives sont arrêtés${NC}"
        return 0
    else
        echo -e "${RED}⚠️  Certains services sont encore actifs${NC}"
        return 1
    fi
}

# Fonction principale
main() {
    log "🛑 Arrêt du système d'alertes prédictives Hanuman..."
    
    stop_anomaly_detection
    stop_health_checks
    cleanup_orphan_processes
    
    sleep 1
    
    if show_final_status; then
        echo -e "${GREEN}✅ Système d'alertes prédictives arrêté avec succès${NC}"
        echo -e "${BLUE}💡 Utilisez './scripts/start-predictive-alerts.sh' pour redémarrer${NC}"
    else
        echo -e "${RED}❌ Arrêt incomplet du système${NC}"
        echo -e "${YELLOW}💡 Vérifiez manuellement les processus restants${NC}"
        exit 1
    fi
}

# Exécution du script principal
main "$@"
