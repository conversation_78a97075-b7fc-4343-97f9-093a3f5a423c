#!/bin/bash

# 💾 HANUMAN - Configuration Système de Backup
# ============================================
# Script pour configurer le système de sauvegarde et recovery automatique
# Jour 4 du Roadmap Excellence 10/10 - Backup & Recovery

set -e

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration des chemins
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
HANUMAN_ROOT="$PROJECT_ROOT/hanuman-unified"
BACKUP_DIR="$HANUMAN_ROOT/backup-system"
HA_DIR="$HANUMAN_ROOT/high-availability"

echo -e "${PURPLE}💾 HANUMAN - Configuration Système de Backup${NC}"
echo -e "${PURPLE}=============================================${NC}"
echo -e "${CYAN}📅 Roadmap Excellence 10/10 - Jour 4${NC}"
echo -e "${CYAN}🎯 Objectif: Backup continu + Recovery <2min${NC}"
echo ""

# Fonction de logging
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}" >&2
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# Configuration du système de backup des agents
configure_agent_backup() {
    log "🤖 Configuration du backup des agents spécialisés..."

    # Service de backup des agents
    cat > "$BACKUP_DIR/agent-backup-service.js" << 'EOF'
/**
 * 💾 HANUMAN - Service de Backup des Agents
 * ========================================
 * Sauvegarde continue des états et modèles IA des agents
 */

const fs = require('fs').promises;
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

class AgentBackupService {
    constructor() {
        this.backupInterval = 5 * 60 * 1000; // 5 minutes
        this.retentionDays = 7; // Garder 7 jours de backups
        this.backupDir = path.join(__dirname, 'agents');
        this.isRunning = false;
        this.agents = [
            'cortex-central',
            'specialized-agents',
            'immune-system',
            'anomaly-detection',
            'health-checks'
        ];
    }

    // Démarrer le service de backup
    async startBackupService() {
        console.log('💾 Démarrage du service de backup des agents...');

        this.isRunning = true;

        // Backup initial
        await this.performFullBackup();

        // Programmer les backups réguliers
        this.backupTimer = setInterval(async () => {
            if (this.isRunning) {
                await this.performIncrementalBackup();
            }
        }, this.backupInterval);

        // Nettoyage quotidien des anciens backups
        this.cleanupTimer = setInterval(async () => {
            if (this.isRunning) {
                await this.cleanupOldBackups();
            }
        }, 24 * 60 * 60 * 1000); // 24 heures

        console.log('✅ Service de backup des agents démarré');
    }

    // Arrêter le service de backup
    stopBackupService() {
        console.log('🛑 Arrêt du service de backup des agents...');

        this.isRunning = false;

        if (this.backupTimer) {
            clearInterval(this.backupTimer);
        }

        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
        }

        console.log('✅ Service de backup arrêté');
    }

    // Effectuer un backup complet
    async performFullBackup() {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupPath = path.join(this.backupDir, `full-backup-${timestamp}`);

        console.log(`💾 Backup complet vers: ${backupPath}`);

        try {
            await fs.mkdir(backupPath, { recursive: true });

            for (const agent of this.agents) {
                await this.backupAgent(agent, backupPath, 'full');
            }

            // Créer un manifeste du backup
            const manifest = {
                type: 'full',
                timestamp: new Date().toISOString(),
                agents: this.agents,
                size: await this.calculateBackupSize(backupPath),
                checksum: await this.calculateChecksum(backupPath)
            };

            await fs.writeFile(
                path.join(backupPath, 'manifest.json'),
                JSON.stringify(manifest, null, 2)
            );

            console.log(`✅ Backup complet terminé: ${manifest.size} bytes`);
            return backupPath;

        } catch (error) {
            console.error('❌ Erreur backup complet:', error.message);
            throw error;
        }
    }

    // Effectuer un backup incrémental
    async performIncrementalBackup() {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupPath = path.join(this.backupDir, `incremental-backup-${timestamp}`);

        console.log(`💾 Backup incrémental vers: ${backupPath}`);

        try {
            await fs.mkdir(backupPath, { recursive: true });

            let hasChanges = false;

            for (const agent of this.agents) {
                const changed = await this.backupAgent(agent, backupPath, 'incremental');
                if (changed) hasChanges = true;
            }

            if (hasChanges) {
                // Créer un manifeste du backup
                const manifest = {
                    type: 'incremental',
                    timestamp: new Date().toISOString(),
                    agents: this.agents,
                    size: await this.calculateBackupSize(backupPath),
                    checksum: await this.calculateChecksum(backupPath)
                };

                await fs.writeFile(
                    path.join(backupPath, 'manifest.json'),
                    JSON.stringify(manifest, null, 2)
                );

                console.log(`✅ Backup incrémental terminé: ${manifest.size} bytes`);
            } else {
                // Supprimer le dossier vide si aucun changement
                await fs.rmdir(backupPath);
                console.log('ℹ️  Aucun changement détecté - backup incrémental ignoré');
            }

        } catch (error) {
            console.error('❌ Erreur backup incrémental:', error.message);
        }
    }

    // Sauvegarder un agent spécifique
    async backupAgent(agentName, backupPath, type) {
        const agentDir = path.join(backupPath, agentName);
        await fs.mkdir(agentDir, { recursive: true });

        let hasChanges = false;

        try {
            // Backup des fichiers de configuration
            const configFiles = await this.findAgentFiles(agentName, ['.js', '.json', '.yml', '.yaml']);
            for (const file of configFiles) {
                if (type === 'full' || await this.hasFileChanged(file)) {
                    await this.copyFile(file, path.join(agentDir, path.basename(file)));
                    hasChanges = true;
                }
            }

            // Backup des données d'état (si l'agent est actif)
            const stateData = await this.captureAgentState(agentName);
            if (stateData) {
                await fs.writeFile(
                    path.join(agentDir, 'state.json'),
                    JSON.stringify(stateData, null, 2)
                );
                hasChanges = true;
            }

            // Backup des modèles IA (si applicable)
            const modelData = await this.captureAgentModels(agentName);
            if (modelData) {
                await fs.writeFile(
                    path.join(agentDir, 'models.json'),
                    JSON.stringify(modelData, null, 2)
                );
                hasChanges = true;
            }

            if (hasChanges) {
                console.log(`  ✅ Agent ${agentName} sauvegardé`);
            }

            return hasChanges;

        } catch (error) {
            console.error(`  ❌ Erreur backup agent ${agentName}:`, error.message);
            return false;
        }
    }

    // Trouver les fichiers d'un agent
    async findAgentFiles(agentName, extensions) {
        const files = [];
        const searchPaths = [
            path.join(__dirname, '..', 'specialized-agents', agentName),
            path.join(__dirname, '..', 'cortex-central'),
            path.join(__dirname, '..', 'immune-system'),
            path.join(__dirname, '..', 'monitoring', 'predictive-alerts')
        ];

        for (const searchPath of searchPaths) {
            try {
                const items = await fs.readdir(searchPath, { withFileTypes: true });
                for (const item of items) {
                    if (item.isFile()) {
                        const ext = path.extname(item.name);
                        if (extensions.includes(ext)) {
                            files.push(path.join(searchPath, item.name));
                        }
                    }
                }
            } catch (error) {
                // Dossier n'existe pas, continuer
            }
        }

        return files;
    }

    // Capturer l'état d'un agent
    async captureAgentState(agentName) {
        try {
            // Simuler la capture d'état (en production, connecter aux vrais agents)
            const state = {
                agentName: agentName,
                timestamp: new Date().toISOString(),
                status: 'active',
                metrics: {
                    uptime: Math.floor(Math.random() * 86400),
                    memoryUsage: Math.floor(Math.random() * 100),
                    cpuUsage: Math.floor(Math.random() * 100)
                },
                configuration: {
                    version: '1.0.0',
                    lastUpdate: new Date().toISOString()
                }
            };

            return state;
        } catch (error) {
            console.error(`Erreur capture état ${agentName}:`, error.message);
            return null;
        }
    }

    // Capturer les modèles IA d'un agent
    async captureAgentModels(agentName) {
        if (agentName === 'anomaly-detection') {
            try {
                // Simuler la capture des modèles IA
                const models = {
                    agentName: agentName,
                    timestamp: new Date().toISOString(),
                    models: {
                        anomalyDetection: {
                            type: 'statistical',
                            parameters: {
                                threshold: 2.0,
                                windowSize: 50
                            },
                            trainingData: 'compressed_data_placeholder'
                        }
                    }
                };

                return models;
            } catch (error) {
                console.error(`Erreur capture modèles ${agentName}:`, error.message);
                return null;
            }
        }

        return null;
    }

    // Vérifier si un fichier a changé
    async hasFileChanged(filePath) {
        try {
            const stats = await fs.stat(filePath);
            const lastModified = stats.mtime.getTime();
            const fiveMinutesAgo = Date.now() - (5 * 60 * 1000);

            return lastModified > fiveMinutesAgo;
        } catch (error) {
            return false;
        }
    }

    // Copier un fichier
    async copyFile(source, destination) {
        try {
            await fs.copyFile(source, destination);
        } catch (error) {
            console.error(`Erreur copie ${source} -> ${destination}:`, error.message);
        }
    }

    // Calculer la taille d'un backup
    async calculateBackupSize(backupPath) {
        try {
            const { stdout } = await execAsync(`du -sb "${backupPath}"`);
            return parseInt(stdout.split('\t')[0]);
        } catch (error) {
            return 0;
        }
    }

    // Calculer le checksum d'un backup
    async calculateChecksum(backupPath) {
        try {
            const { stdout } = await execAsync(`find "${backupPath}" -type f -exec md5sum {} \\; | md5sum`);
            return stdout.split(' ')[0];
        } catch (error) {
            return 'unknown';
        }
    }

    // Nettoyer les anciens backups
    async cleanupOldBackups() {
        console.log('🧹 Nettoyage des anciens backups...');

        try {
            const items = await fs.readdir(this.backupDir, { withFileTypes: true });
            const cutoffDate = new Date(Date.now() - (this.retentionDays * 24 * 60 * 60 * 1000));

            let deletedCount = 0;

            for (const item of items) {
                if (item.isDirectory()) {
                    const itemPath = path.join(this.backupDir, item.name);
                    const stats = await fs.stat(itemPath);

                    if (stats.mtime < cutoffDate) {
                        await fs.rmdir(itemPath, { recursive: true });
                        deletedCount++;
                        console.log(`  🗑️  Supprimé: ${item.name}`);
                    }
                }
            }

            console.log(`✅ Nettoyage terminé: ${deletedCount} backup(s) supprimé(s)`);

        } catch (error) {
            console.error('❌ Erreur nettoyage:', error.message);
        }
    }

    // Obtenir le statut du service
    getStatus() {
        return {
            isRunning: this.isRunning,
            backupInterval: this.backupInterval,
            retentionDays: this.retentionDays,
            agents: this.agents,
            lastBackup: this.lastBackup || 'N/A'
        };
    }
}

module.exports = AgentBackupService;

// Démarrage automatique si exécuté directement
if (require.main === module) {
    const backupService = new AgentBackupService();

    backupService.startBackupService();

    // Afficher le statut toutes les 10 minutes
    setInterval(() => {
        console.log('📊 Statut backup service:', backupService.getStatus());
    }, 10 * 60 * 1000);

    // Gestion propre de l'arrêt
    process.on('SIGINT', () => {
        console.log('\n🛑 Arrêt du service de backup...');
        backupService.stopBackupService();
        process.exit(0);
    });
}
EOF

    success "Service de backup des agents configuré"
}

# Configuration du système de recovery
configure_recovery_system() {
    log "🔄 Configuration du système de recovery..."

    # Service de recovery automatique
    cat > "$BACKUP_DIR/recovery/recovery-service.js" << 'EOF'
/**
 * 🔄 HANUMAN - Service de Recovery Automatique
 * ===========================================
 * Recovery automatique des agents en cas de panne
 */

const fs = require('fs').promises;
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

class RecoveryService {
    constructor() {
        this.backupDir = path.join(__dirname, '..', 'agents');
        this.recoveryTimeout = 2 * 60 * 1000; // 2 minutes max pour recovery
        this.isRecovering = false;
    }

    // Récupérer un agent depuis le backup le plus récent
    async recoverAgent(agentName) {
        if (this.isRecovering) {
            console.log('⚠️  Recovery déjà en cours, attente...');
            return false;
        }

        this.isRecovering = true;
        const startTime = Date.now();

        console.log(`🔄 Démarrage recovery de l'agent: ${agentName}`);

        try {
            // Trouver le backup le plus récent
            const latestBackup = await this.findLatestBackup(agentName);
            if (!latestBackup) {
                throw new Error(`Aucun backup trouvé pour l'agent ${agentName}`);
            }

            console.log(`📁 Backup trouvé: ${latestBackup}`);

            // Restaurer les fichiers
            await this.restoreAgentFiles(agentName, latestBackup);

            // Restaurer l'état
            await this.restoreAgentState(agentName, latestBackup);

            // Redémarrer l'agent
            await this.restartAgent(agentName);

            const recoveryTime = Date.now() - startTime;
            console.log(`✅ Recovery de ${agentName} terminé en ${recoveryTime}ms`);

            return true;

        } catch (error) {
            console.error(`❌ Erreur recovery ${agentName}:`, error.message);
            return false;
        } finally {
            this.isRecovering = false;
        }
    }

    // Trouver le backup le plus récent pour un agent
    async findLatestBackup(agentName) {
        try {
            const items = await fs.readdir(this.backupDir, { withFileTypes: true });
            const backupDirs = items
                .filter(item => item.isDirectory())
                .map(item => item.name)
                .sort()
                .reverse();

            for (const backupDir of backupDirs) {
                const agentBackupPath = path.join(this.backupDir, backupDir, agentName);
                try {
                    await fs.access(agentBackupPath);
                    return path.join(this.backupDir, backupDir);
                } catch {
                    continue;
                }
            }

            return null;
        } catch (error) {
            console.error('Erreur recherche backup:', error.message);
            return null;
        }
    }

    // Restaurer les fichiers d'un agent
    async restoreAgentFiles(agentName, backupPath) {
        console.log(`📂 Restauration des fichiers de ${agentName}...`);

        const agentBackupPath = path.join(backupPath, agentName);
        const targetPaths = [
            path.join(__dirname, '..', '..', 'specialized-agents', agentName),
            path.join(__dirname, '..', '..', 'cortex-central'),
            path.join(__dirname, '..', '..', 'immune-system')
        ];

        try {
            const files = await fs.readdir(agentBackupPath);

            for (const file of files) {
                if (file === 'state.json' || file === 'models.json') continue;

                const sourcePath = path.join(agentBackupPath, file);

                // Trouver le bon répertoire de destination
                for (const targetPath of targetPaths) {
                    try {
                        await fs.access(targetPath);
                        const destPath = path.join(targetPath, file);
                        await fs.copyFile(sourcePath, destPath);
                        console.log(`  ✅ Restauré: ${file}`);
                        break;
                    } catch {
                        continue;
                    }
                }
            }

        } catch (error) {
            console.error(`Erreur restauration fichiers ${agentName}:`, error.message);
        }
    }

    // Restaurer l'état d'un agent
    async restoreAgentState(agentName, backupPath) {
        console.log(`🧠 Restauration de l'état de ${agentName}...`);

        try {
            const statePath = path.join(backupPath, agentName, 'state.json');
            const stateData = await fs.readFile(statePath, 'utf8');
            const state = JSON.parse(stateData);

            console.log(`  ✅ État restauré: ${state.timestamp}`);

            // Restaurer les modèles IA si disponibles
            const modelsPath = path.join(backupPath, agentName, 'models.json');
            try {
                const modelsData = await fs.readFile(modelsPath, 'utf8');
                const models = JSON.parse(modelsData);
                console.log(`  ✅ Modèles IA restaurés: ${models.timestamp}`);
            } catch {
                // Pas de modèles, continuer
            }

        } catch (error) {
            console.error(`Erreur restauration état ${agentName}:`, error.message);
        }
    }

    // Redémarrer un agent
    async restartAgent(agentName) {
        console.log(`🚀 Redémarrage de l'agent ${agentName}...`);

        try {
            // Simuler le redémarrage (en production, utiliser les vrais commandes)
            await new Promise(resolve => setTimeout(resolve, 1000));

            console.log(`  ✅ Agent ${agentName} redémarré`);

        } catch (error) {
            console.error(`Erreur redémarrage ${agentName}:`, error.message);
        }
    }

    // Recovery complet du système
    async performFullRecovery() {
        console.log('🔄 Démarrage du recovery complet du système...');

        const agents = [
            'cortex-central',
            'specialized-agents',
            'immune-system',
            'anomaly-detection',
            'health-checks'
        ];

        const results = [];

        for (const agent of agents) {
            const success = await this.recoverAgent(agent);
            results.push({ agent, success });
        }

        const successCount = results.filter(r => r.success).length;
        console.log(`✅ Recovery complet terminé: ${successCount}/${agents.length} agents récupérés`);

        return results;
    }
}

module.exports = RecoveryService;

// Démarrage automatique si exécuté directement
if (require.main === module) {
    const recoveryService = new RecoveryService();

    // Exemple d'utilisation
    const agentToRecover = process.argv[2];
    if (agentToRecover) {
        recoveryService.recoverAgent(agentToRecover);
    } else {
        console.log('Usage: node recovery-service.js <agent-name>');
        console.log('Ou: node recovery-service.js full (pour recovery complet)');
    }
}
EOF

    success "Système de recovery configuré"
}

# Configuration des scripts de démarrage
configure_startup_scripts() {
    log "🚀 Configuration des scripts de démarrage backup..."

    # Script de démarrage du service de backup
    cat > "$BACKUP_DIR/start-backup-service.sh" << 'EOF'
#!/bin/bash

echo "💾 Démarrage du service de backup Hanuman..."

# Démarrer le service de backup des agents
nohup node agent-backup-service.js > ../logs/alerts/backup-service.log 2>&1 &
echo $! > backup-service.pid

echo "✅ Service de backup démarré (PID: $(cat backup-service.pid))"
echo "📋 Logs: ../logs/alerts/backup-service.log"
EOF

    chmod +x "$BACKUP_DIR/start-backup-service.sh"

    # Script d'arrêt du service de backup
    cat > "$BACKUP_DIR/stop-backup-service.sh" << 'EOF'
#!/bin/bash

echo "🛑 Arrêt du service de backup Hanuman..."

if [[ -f backup-service.pid ]]; then
    PID=$(cat backup-service.pid)
    if ps -p $PID > /dev/null 2>&1; then
        kill $PID
        echo "✅ Service de backup arrêté (PID: $PID)"
        rm backup-service.pid
    else
        echo "⚠️  Service de backup déjà arrêté"
        rm backup-service.pid
    fi
else
    echo "⚠️  Fichier PID non trouvé"
fi
EOF

    chmod +x "$BACKUP_DIR/stop-backup-service.sh"

    success "Scripts de démarrage backup configurés"
}

# Fonction principale
main() {
    log "🚀 Démarrage de la configuration du système de backup..."

    # Créer la structure si elle n'existe pas
    mkdir -p "$BACKUP_DIR/agents"
    mkdir -p "$BACKUP_DIR/data"
    mkdir -p "$BACKUP_DIR/configs"
    mkdir -p "$BACKUP_DIR/recovery"

    configure_agent_backup
    configure_recovery_system
    configure_startup_scripts

    success "🎉 Configuration du système de backup terminée!"
    info "📋 Fichiers créés:"
    info "   - $BACKUP_DIR/agent-backup-service.js"
    info "   - $BACKUP_DIR/recovery/recovery-service.js"
    info "   - $BACKUP_DIR/start-backup-service.sh"
    info "   - $BACKUP_DIR/stop-backup-service.sh"
    info ""
    info "📋 Prochaines étapes:"
    info "   1. Tester: ./scripts/test-failover-scenarios.sh"
    info "   2. Valider: ./scripts/validate-recovery-system.sh"
}

# Exécution du script principal
main "$@"
