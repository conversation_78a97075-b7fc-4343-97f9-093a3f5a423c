#!/bin/bash

# 📊 HANUMAN - Statut des Alertes Prédictives
# ===========================================
# Script pour vérifier le statut du système d'alerting prédictif

set -e

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration des chemins
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
HANUMAN_ROOT="$PROJECT_ROOT/hanuman-unified"
MONITORING_DIR="$HANUMAN_ROOT/monitoring"
ALERTS_DIR="$MONITORING_DIR/predictive-alerts"

echo -e "${PURPLE}📊 HANUMAN - Statut Alertes Prédictives${NC}"
echo -e "${PURPLE}======================================${NC}"
echo -e "${CYAN}⏰ $(date)${NC}"
echo ""

# Fonction de logging
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

# Vérifier le statut des processus
check_process_status() {
    echo -e "${BLUE}🔍 STATUT DES PROCESSUS${NC}"
    echo -e "${BLUE}======================${NC}"
    echo ""
    
    # Vérifier l'IA de détection d'anomalies
    local ai_status="❌ ARRÊTÉ"
    local ai_pid=""
    local ai_uptime=""
    
    if [[ -f "$HANUMAN_ROOT/logs/alerts/anomaly-ai.pid" ]]; then
        ai_pid=$(cat "$HANUMAN_ROOT/logs/alerts/anomaly-ai.pid")
        if ps -p $ai_pid > /dev/null 2>&1; then
            ai_status="✅ ACTIF"
            ai_uptime=$(ps -o etime= -p $ai_pid | tr -d ' ')
        else
            ai_status="❌ ARRÊTÉ (PID obsolète)"
        fi
    fi
    
    echo -e "${CYAN}🧠 IA Détection Anomalies:${NC} $ai_status"
    [[ -n "$ai_pid" ]] && echo -e "   📍 PID: $ai_pid"
    [[ -n "$ai_uptime" ]] && echo -e "   ⏱️  Uptime: $ai_uptime"
    echo ""
    
    # Vérifier les health checks
    local health_status="❌ ARRÊTÉ"
    local health_pid=""
    local health_uptime=""
    
    if [[ -f "$HANUMAN_ROOT/logs/alerts/health-checks.pid" ]]; then
        health_pid=$(cat "$HANUMAN_ROOT/logs/alerts/health-checks.pid")
        if ps -p $health_pid > /dev/null 2>&1; then
            health_status="✅ ACTIF"
            health_uptime=$(ps -o etime= -p $health_pid | tr -d ' ')
        else
            health_status="❌ ARRÊTÉ (PID obsolète)"
        fi
    fi
    
    echo -e "${CYAN}🏥 Health Checks Avancés:${NC} $health_status"
    [[ -n "$health_pid" ]] && echo -e "   📍 PID: $health_pid"
    [[ -n "$health_uptime" ]] && echo -e "   ⏱️  Uptime: $health_uptime"
    echo ""
}

# Vérifier les logs récents
check_recent_logs() {
    echo -e "${BLUE}📋 LOGS RÉCENTS${NC}"
    echo -e "${BLUE}===============${NC}"
    echo ""
    
    # Logs de l'IA d'anomalies
    if [[ -f "$HANUMAN_ROOT/logs/alerts/anomaly-ai.log" ]]; then
        echo -e "${CYAN}🧠 IA Détection Anomalies (5 dernières lignes):${NC}"
        tail -5 "$HANUMAN_ROOT/logs/alerts/anomaly-ai.log" 2>/dev/null | sed 's/^/   /' || echo "   Aucun log disponible"
        echo ""
    fi
    
    # Logs des health checks
    if [[ -f "$HANUMAN_ROOT/logs/alerts/health-checks.log" ]]; then
        echo -e "${CYAN}🏥 Health Checks (5 dernières lignes):${NC}"
        tail -5 "$HANUMAN_ROOT/logs/alerts/health-checks.log" 2>/dev/null | sed 's/^/   /' || echo "   Aucun log disponible"
        echo ""
    fi
    
    # Logs d'auto-correction
    if [[ -f "$HANUMAN_ROOT/logs/alerts/auto-correction.log" ]]; then
        echo -e "${CYAN}🔧 Auto-correction (5 dernières lignes):${NC}"
        tail -5 "$HANUMAN_ROOT/logs/alerts/auto-correction.log" 2>/dev/null | sed 's/^/   /' || echo "   Aucun log disponible"
        echo ""
    fi
}

# Vérifier la configuration
check_configuration() {
    echo -e "${BLUE}⚙️  CONFIGURATION${NC}"
    echo -e "${BLUE}=================${NC}"
    echo ""
    
    # Vérifier les fichiers de configuration
    local config_files=(
        "$ALERTS_DIR/rules/predictive-alerts.yml:Règles Prometheus"
        "$ALERTS_DIR/alertmanager-config.yml:Configuration AlertManager"
        "$ALERTS_DIR/anomaly-detection.js:IA Détection Anomalies"
        "$ALERTS_DIR/health-checks.js:Health Checks"
        "$ALERTS_DIR/scripts/auto-correction.sh:Script Auto-correction"
    )
    
    for config_entry in "${config_files[@]}"; do
        local file_path="${config_entry%:*}"
        local file_desc="${config_entry#*:}"
        
        if [[ -f "$file_path" ]]; then
            echo -e "${GREEN}✅ $file_desc${NC}"
        else
            echo -e "${RED}❌ $file_desc${NC}"
        fi
    done
    echo ""
    
    # Vérifier les variables d'environnement
    if [[ -f "$ALERTS_DIR/.env" ]]; then
        echo -e "${GREEN}✅ Fichier d'environnement configuré${NC}"
        echo -e "${CYAN}   📁 $ALERTS_DIR/.env${NC}"
    else
        echo -e "${YELLOW}⚠️  Fichier d'environnement manquant${NC}"
    fi
    echo ""
}

# Vérifier les métriques système
check_system_metrics() {
    echo -e "${BLUE}📊 MÉTRIQUES SYSTÈME${NC}"
    echo -e "${BLUE}====================${NC}"
    echo ""
    
    # Utilisation CPU
    local cpu_usage=$(top -l 1 | grep "CPU usage" | awk '{print $3}' | sed 's/%//' || echo "N/A")
    echo -e "${CYAN}🖥️  CPU Usage:${NC} ${cpu_usage}%"
    
    # Utilisation mémoire
    local memory_info=$(top -l 1 | grep "PhysMem" || echo "N/A")
    echo -e "${CYAN}💾 Memory:${NC} $memory_info"
    
    # Espace disque
    local disk_usage=$(df -h . | tail -1 | awk '{print $5}' || echo "N/A")
    echo -e "${CYAN}💿 Disk Usage:${NC} $disk_usage"
    
    # Charge système
    local load_avg=$(uptime | awk -F'load averages:' '{print $2}' || echo "N/A")
    echo -e "${CYAN}⚖️  Load Average:${NC} $load_avg"
    echo ""
}

# Vérifier les alertes actives
check_active_alerts() {
    echo -e "${BLUE}🚨 ALERTES ACTIVES${NC}"
    echo -e "${BLUE}==================${NC}"
    echo ""
    
    # Simuler la vérification d'alertes (en production, connecter à Prometheus/AlertManager)
    local alert_count=0
    
    # Vérifier les logs pour des alertes récentes
    if [[ -f "$HANUMAN_ROOT/logs/alerts/anomaly-ai.log" ]]; then
        alert_count=$(grep -c "🚨" "$HANUMAN_ROOT/logs/alerts/anomaly-ai.log" 2>/dev/null | tail -1 || echo "0")
    fi
    
    if [[ $alert_count -gt 0 ]]; then
        echo -e "${YELLOW}⚠️  $alert_count alerte(s) détectée(s) récemment${NC}"
        echo -e "${CYAN}   📋 Consultez les logs pour plus de détails${NC}"
    else
        echo -e "${GREEN}✅ Aucune alerte active${NC}"
    fi
    echo ""
}

# Générer un résumé du statut
generate_status_summary() {
    echo -e "${BLUE}📋 RÉSUMÉ DU STATUT${NC}"
    echo -e "${BLUE}===================${NC}"
    echo ""
    
    local overall_status="🟢 OPÉRATIONNEL"
    local issues=0
    
    # Vérifier les processus critiques
    if [[ ! -f "$HANUMAN_ROOT/logs/alerts/anomaly-ai.pid" ]] || ! ps -p $(cat "$HANUMAN_ROOT/logs/alerts/anomaly-ai.pid" 2>/dev/null) > /dev/null 2>&1; then
        overall_status="🟡 DÉGRADÉ"
        ((issues++))
    fi
    
    if [[ ! -f "$HANUMAN_ROOT/logs/alerts/health-checks.pid" ]] || ! ps -p $(cat "$HANUMAN_ROOT/logs/alerts/health-checks.pid" 2>/dev/null) > /dev/null 2>&1; then
        overall_status="🟡 DÉGRADÉ"
        ((issues++))
    fi
    
    if [[ $issues -ge 2 ]]; then
        overall_status="🔴 CRITIQUE"
    fi
    
    echo -e "${CYAN}🕉️  Statut Global:${NC} $overall_status"
    echo -e "${CYAN}📊 Dashboard:${NC} http://localhost:3000/d/hanuman-predictive"
    echo -e "${CYAN}📁 Logs:${NC} $HANUMAN_ROOT/logs/alerts/"
    echo -e "${CYAN}⚙️  Configuration:${NC} $ALERTS_DIR"
    echo ""
    
    if [[ $issues -eq 0 ]]; then
        echo -e "${GREEN}🎉 Système d'alertes prédictives entièrement opérationnel${NC}"
    else
        echo -e "${YELLOW}⚠️  $issues problème(s) détecté(s)${NC}"
        echo -e "${BLUE}💡 Utilisez './scripts/start-predictive-alerts.sh' pour redémarrer${NC}"
    fi
}

# Fonction principale
main() {
    check_process_status
    check_recent_logs
    check_configuration
    check_system_metrics
    check_active_alerts
    generate_status_summary
    
    echo -e "${PURPLE}🕉️  Hanuman surveille et protège Retreat And Be${NC}"
}

# Exécution du script principal
main "$@"
