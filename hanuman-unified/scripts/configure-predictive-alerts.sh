#!/bin/bash

# 🚨 HANUMAN - Configuration des Alertes Prédictives
# ================================================
# Script pour configurer le système d'alerting prédictif avancé
# Jour 3 du Roadmap Excellence 10/10 - Monitoring Hanuman Avancé

set -e

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration des chemins
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
HANUMAN_ROOT="$PROJECT_ROOT/hanuman-unified"
RB2_ROOT="$PROJECT_ROOT/Projet-RB2"
MONITORING_DIR="$HANUMAN_ROOT/monitoring"
ALERTS_DIR="$MONITORING_DIR/predictive-alerts"
CONFIG_DIR="$HANUMAN_ROOT/config"

echo -e "${PURPLE}🕉️  HANUMAN - Configuration Alertes Prédictives${NC}"
echo -e "${PURPLE}================================================${NC}"
echo -e "${CYAN}📅 Roadmap Excellence 10/10 - Jour 3${NC}"
echo -e "${CYAN}🎯 Objectif: Alerting Prédictif + Auto-correction${NC}"
echo ""

# Fonction de logging
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}" >&2
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

# Vérification des prérequis
check_prerequisites() {
    log "🔍 Vérification des prérequis..."

    # Vérifier Node.js
    if ! command -v node &> /dev/null; then
        error "Node.js n'est pas installé"
        exit 1
    fi

    # Vérifier npm
    if ! command -v npm &> /dev/null; then
        error "npm n'est pas installé"
        exit 1
    fi

    # Vérifier Docker (optionnel pour Prometheus/Grafana)
    if ! command -v docker &> /dev/null; then
        warning "Docker n'est pas installé - certaines fonctionnalités seront limitées"
    fi

    # Vérifier curl
    if ! command -v curl &> /dev/null; then
        error "curl n'est pas installé"
        exit 1
    fi

    log "✅ Prérequis validés"
}

# Création de la structure des dossiers
create_directory_structure() {
    log "📁 Création de la structure des dossiers..."

    mkdir -p "$MONITORING_DIR"
    mkdir -p "$ALERTS_DIR"
    mkdir -p "$ALERTS_DIR/rules"
    mkdir -p "$ALERTS_DIR/templates"
    mkdir -p "$ALERTS_DIR/scripts"
    mkdir -p "$ALERTS_DIR/ml-models"
    mkdir -p "$ALERTS_DIR/dashboards"
    mkdir -p "$CONFIG_DIR"
    mkdir -p "$HANUMAN_ROOT/logs/alerts"

    log "✅ Structure des dossiers créée"
}

# Configuration des règles d'alerting prédictif
configure_predictive_rules() {
    log "🧠 Configuration des règles d'alerting prédictif..."

    # Règles Prometheus pour alerting prédictif
    cat > "$ALERTS_DIR/rules/predictive-alerts.yml" << 'EOF'
groups:
  - name: hanuman_predictive_alerts
    interval: 30s
    rules:
      # Prédiction de surcharge CPU
      - alert: CPUOverloadPredicted
        expr: predict_linear(cpu_usage_percent[10m], 300) > 80
        for: 2m
        labels:
          severity: warning
          component: cortex-central
          prediction: true
        annotations:
          summary: "Surcharge CPU prédite dans 5 minutes"
          description: "Le CPU du cortex central devrait atteindre {{ $value }}% dans 5 minutes"
          runbook: "https://hanuman.docs/runbooks/cpu-overload"
          auto_action: "scale_cortex_replicas"

      # Prédiction de fuite mémoire
      - alert: MemoryLeakPredicted
        expr: predict_linear(memory_usage_bytes[15m], 600) > 8e9
        for: 3m
        labels:
          severity: critical
          component: specialized-agents
          prediction: true
        annotations:
          summary: "Fuite mémoire prédite dans 10 minutes"
          description: "La mémoire devrait atteindre {{ $value | humanize }}B dans 10 minutes"
          runbook: "https://hanuman.docs/runbooks/memory-leak"
          auto_action: "restart_leaking_agents"

      # Prédiction de dégradation performance
      - alert: PerformanceDegradationPredicted
        expr: predict_linear(response_time_p95[20m], 900) > 2000
        for: 5m
        labels:
          severity: warning
          component: api-gateway
          prediction: true
        annotations:
          summary: "Dégradation performance prédite dans 15 minutes"
          description: "Le temps de réponse P95 devrait atteindre {{ $value }}ms dans 15 minutes"
          runbook: "https://hanuman.docs/runbooks/performance-degradation"
          auto_action: "optimize_cache_strategy"

      # Détection d'anomalies comportementales
      - alert: BehavioralAnomalyDetected
        expr: |
          (
            abs(rate(http_requests_total[5m]) - rate(http_requests_total[5m] offset 1w)) /
            rate(http_requests_total[5m] offset 1w)
          ) > 0.5
        for: 2m
        labels:
          severity: warning
          component: traffic-analysis
          anomaly: true
        annotations:
          summary: "Anomalie comportementale détectée"
          description: "Le trafic diffère de {{ $value | humanizePercentage }} par rapport à la semaine dernière"
          runbook: "https://hanuman.docs/runbooks/behavioral-anomaly"
          auto_action: "analyze_traffic_pattern"

      # Prédiction de panne agent
      - alert: AgentFailurePredicted
        expr: |
          (
            rate(agent_heartbeat_failures[10m]) > 0.1 and
            predict_linear(agent_heartbeat_failures[10m], 300) > 0.5
          )
        for: 1m
        labels:
          severity: critical
          component: agent-health
          prediction: true
        annotations:
          summary: "Panne d'agent prédite dans 5 minutes"
          description: "L'agent {{ $labels.agent_name }} montre des signes de défaillance"
          runbook: "https://hanuman.docs/runbooks/agent-failure"
          auto_action: "prepare_agent_backup"

      # Détection de surcharge réseau
      - alert: NetworkCongestionPredicted
        expr: predict_linear(network_bytes_total[5m], 180) > 1e9
        for: 2m
        labels:
          severity: warning
          component: network
          prediction: true
        annotations:
          summary: "Congestion réseau prédite dans 3 minutes"
          description: "Le trafic réseau devrait atteindre {{ $value | humanize }}B/s dans 3 minutes"
          runbook: "https://hanuman.docs/runbooks/network-congestion"
          auto_action: "enable_traffic_shaping"

      # Prédiction de saturation disque
      - alert: DiskSaturationPredicted
        expr: predict_linear(disk_usage_percent[30m], 1800) > 90
        for: 5m
        labels:
          severity: critical
          component: storage
          prediction: true
        annotations:
          summary: "Saturation disque prédite dans 30 minutes"
          description: "L'utilisation disque devrait atteindre {{ $value }}% dans 30 minutes"
          runbook: "https://hanuman.docs/runbooks/disk-saturation"
          auto_action: "cleanup_old_logs"

      # Détection d'anomalies de sécurité
      - alert: SecurityAnomalyDetected
        expr: |
          (
            rate(failed_auth_attempts[5m]) > 10 or
            rate(suspicious_requests[5m]) > 5
          )
        for: 1m
        labels:
          severity: critical
          component: security
          anomaly: true
        annotations:
          summary: "Anomalie de sécurité détectée"
          description: "Activité suspecte détectée: {{ $value }} tentatives/requêtes par minute"
          runbook: "https://hanuman.docs/runbooks/security-anomaly"
          auto_action: "enable_enhanced_security"
EOF

    log "✅ Règles d'alerting prédictif configurées"
}

# Configuration du système d'auto-correction
configure_auto_correction() {
    log "🔧 Configuration du système d'auto-correction..."

    # Script d'auto-correction principal
    cat > "$ALERTS_DIR/scripts/auto-correction.sh" << 'EOF'
#!/bin/bash

# 🔧 HANUMAN - Système d'Auto-correction
# =====================================

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
HANUMAN_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"
LOG_FILE="$HANUMAN_ROOT/logs/alerts/auto-correction.log"

# Fonction de logging
log_action() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_FILE"
    echo "🔧 [AUTO-CORRECTION] $1"
}

# Actions d'auto-correction
case "$1" in
    "scale_cortex_replicas")
        log_action "Scaling cortex replicas due to predicted CPU overload"
        # Augmenter le nombre de répliques du cortex central
        kubectl scale deployment cortex-central --replicas=3 -n hanuman || true
        ;;

    "restart_leaking_agents")
        log_action "Restarting agents due to predicted memory leak"
        # Redémarrer les agents avec fuite mémoire
        kubectl rollout restart deployment/specialized-agents -n hanuman || true
        ;;

    "optimize_cache_strategy")
        log_action "Optimizing cache strategy due to predicted performance degradation"
        # Optimiser la stratégie de cache
        curl -X POST http://localhost:3001/api/cache/optimize || true
        ;;

    "analyze_traffic_pattern")
        log_action "Analyzing traffic pattern due to behavioral anomaly"
        # Analyser les patterns de trafic
        node "$HANUMAN_ROOT/specialized-agents/traffic-analyzer/analyze.js" || true
        ;;

    "prepare_agent_backup")
        log_action "Preparing agent backup due to predicted failure"
        # Préparer un agent de backup
        kubectl create deployment agent-backup --image=hanuman/agent:latest -n hanuman || true
        ;;

    "enable_traffic_shaping")
        log_action "Enabling traffic shaping due to predicted network congestion"
        # Activer le traffic shaping
        curl -X POST http://localhost:3001/api/network/traffic-shaping/enable || true
        ;;

    "cleanup_old_logs")
        log_action "Cleaning up old logs due to predicted disk saturation"
        # Nettoyer les anciens logs
        find "$HANUMAN_ROOT/logs" -name "*.log" -mtime +7 -delete || true
        ;;

    "enable_enhanced_security")
        log_action "Enabling enhanced security due to security anomaly"
        # Activer la sécurité renforcée
        curl -X POST http://localhost:3001/api/security/enhanced-mode/enable || true
        ;;

    *)
        log_action "Unknown auto-correction action: $1"
        exit 1
        ;;
esac

log_action "Auto-correction action '$1' completed"
EOF

    chmod +x "$ALERTS_DIR/scripts/auto-correction.sh"

    log "✅ Système d'auto-correction configuré"
}

# Configuration de l'IA de détection d'anomalies
configure_anomaly_detection_ai() {
    log "🧠 Configuration de l'IA de détection d'anomalies..."

    # Service de détection d'anomalies avec IA
    cat > "$ALERTS_DIR/anomaly-detection.js" << 'EOF'
/**
 * 🧠 HANUMAN - IA de Détection d'Anomalies
 * ========================================
 * Système d'IA pour détecter les anomalies comportementales
 */

const fs = require('fs');
const path = require('path');

class AnomalyDetectionAI {
    constructor() {
        this.models = new Map();
        this.thresholds = {
            cpu: { normal: [20, 60], warning: 70, critical: 85 },
            memory: { normal: [30, 70], warning: 80, critical: 90 },
            network: { normal: [0, 100], warning: 500, critical: 1000 },
            response_time: { normal: [50, 200], warning: 500, critical: 1000 }
        };
        this.learningData = [];
        this.anomalyHistory = [];
    }

    // Analyser les métriques en temps réel
    async analyzeMetrics(metrics) {
        const anomalies = [];

        for (const [metricName, value] of Object.entries(metrics)) {
            const anomaly = await this.detectAnomaly(metricName, value);
            if (anomaly) {
                anomalies.push(anomaly);
            }
        }

        return anomalies;
    }

    // Détecter une anomalie pour une métrique spécifique
    async detectAnomaly(metricName, value) {
        const threshold = this.thresholds[metricName];
        if (!threshold) return null;

        // Détection basée sur les seuils statiques
        let severity = 'normal';
        if (value > threshold.critical) {
            severity = 'critical';
        } else if (value > threshold.warning) {
            severity = 'warning';
        } else if (value < threshold.normal[0] || value > threshold.normal[1]) {
            severity = 'info';
        }

        // Détection basée sur l'apprentissage automatique
        const mlAnomaly = await this.detectMLAnomaly(metricName, value);

        if (severity !== 'normal' || mlAnomaly) {
            const anomaly = {
                metric: metricName,
                value: value,
                severity: severity,
                timestamp: new Date().toISOString(),
                mlDetected: mlAnomaly,
                prediction: await this.predictFutureValue(metricName, value)
            };

            this.anomalyHistory.push(anomaly);
            return anomaly;
        }

        return null;
    }

    // Détection ML basée sur les patterns historiques
    async detectMLAnomaly(metricName, value) {
        const historicalData = this.getHistoricalData(metricName);
        if (historicalData.length < 10) return false;

        // Calcul de la moyenne et écart-type
        const mean = historicalData.reduce((a, b) => a + b, 0) / historicalData.length;
        const variance = historicalData.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / historicalData.length;
        const stdDev = Math.sqrt(variance);

        // Détection d'anomalie si la valeur est à plus de 2 écarts-types
        const zScore = Math.abs((value - mean) / stdDev);
        return zScore > 2;
    }

    // Prédire la valeur future d'une métrique
    async predictFutureValue(metricName, currentValue) {
        const historicalData = this.getHistoricalData(metricName);
        if (historicalData.length < 5) return currentValue;

        // Régression linéaire simple pour prédiction
        const n = historicalData.length;
        const x = Array.from({length: n}, (_, i) => i);
        const y = historicalData;

        const sumX = x.reduce((a, b) => a + b, 0);
        const sumY = y.reduce((a, b) => a + b, 0);
        const sumXY = x.reduce((acc, xi, i) => acc + xi * y[i], 0);
        const sumXX = x.reduce((acc, xi) => acc + xi * xi, 0);

        const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
        const intercept = (sumY - slope * sumX) / n;

        // Prédiction pour les 5 prochaines minutes
        const futureX = n + 5;
        return slope * futureX + intercept;
    }

    // Obtenir les données historiques pour une métrique
    getHistoricalData(metricName) {
        return this.learningData
            .filter(data => data.metric === metricName)
            .map(data => data.value)
            .slice(-50); // Garder les 50 dernières valeurs
    }

    // Ajouter des données d'apprentissage
    addLearningData(metricName, value) {
        this.learningData.push({
            metric: metricName,
            value: value,
            timestamp: new Date().toISOString()
        });

        // Garder seulement les 1000 dernières entrées
        if (this.learningData.length > 1000) {
            this.learningData = this.learningData.slice(-1000);
        }
    }

    // Générer un rapport d'anomalies
    generateAnomalyReport() {
        const report = {
            timestamp: new Date().toISOString(),
            totalAnomalies: this.anomalyHistory.length,
            criticalAnomalies: this.anomalyHistory.filter(a => a.severity === 'critical').length,
            warningAnomalies: this.anomalyHistory.filter(a => a.severity === 'warning').length,
            recentAnomalies: this.anomalyHistory.slice(-10),
            recommendations: this.generateRecommendations()
        };

        return report;
    }

    // Générer des recommandations basées sur les anomalies
    generateRecommendations() {
        const recommendations = [];
        const recentAnomalies = this.anomalyHistory.slice(-20);

        // Analyser les patterns d'anomalies
        const metricCounts = {};
        recentAnomalies.forEach(anomaly => {
            metricCounts[anomaly.metric] = (metricCounts[anomaly.metric] || 0) + 1;
        });

        // Générer des recommandations
        for (const [metric, count] of Object.entries(metricCounts)) {
            if (count > 5) {
                recommendations.push({
                    metric: metric,
                    issue: `Anomalies fréquentes détectées (${count} dans les 20 dernières)`,
                    recommendation: this.getRecommendationForMetric(metric),
                    priority: count > 10 ? 'high' : 'medium'
                });
            }
        }

        return recommendations;
    }

    // Obtenir une recommandation pour une métrique spécifique
    getRecommendationForMetric(metric) {
        const recommendations = {
            cpu: "Considérer l'ajout de ressources CPU ou l'optimisation du code",
            memory: "Vérifier les fuites mémoire et optimiser l'utilisation",
            network: "Analyser le trafic réseau et considérer la mise en cache",
            response_time: "Optimiser les requêtes et améliorer les performances"
        };

        return recommendations[metric] || "Analyser la métrique pour identifier la cause";
    }
}

module.exports = AnomalyDetectionAI;
EOF

    log "✅ IA de détection d'anomalies configurée"
}

# Configuration de l'escalation intelligente
configure_intelligent_escalation() {
    log "📢 Configuration de l'escalation intelligente..."

    # Configuration AlertManager pour escalation intelligente
    cat > "$ALERTS_DIR/alertmanager-config.yml" << 'EOF'
global:
  resolve_timeout: 5m
  slack_api_url: '${SLACK_WEBHOOK_URL}'

route:
  group_by: ['alertname', 'component', 'severity']
  group_wait: 10s
  group_interval: 30s
  repeat_interval: 5m
  receiver: 'hanuman-default'
  routes:
    # Alertes critiques - escalation immédiate
    - match:
        severity: critical
      receiver: 'hanuman-critical'
      group_wait: 0s
      repeat_interval: 2m
      routes:
        # Sécurité - escalation spéciale
        - match:
            component: security
          receiver: 'hanuman-security-team'
          group_wait: 0s
          repeat_interval: 1m

        # Cortex Central - escalation DevOps
        - match:
            component: cortex-central
          receiver: 'hanuman-devops-team'
          group_wait: 5s
          repeat_interval: 2m

    # Alertes prédictives - escalation préventive
    - match:
        prediction: "true"
      receiver: 'hanuman-predictive'
      group_wait: 30s
      repeat_interval: 10m

    # Anomalies comportementales - escalation analytique
    - match:
        anomaly: "true"
      receiver: 'hanuman-anomaly-team'
      group_wait: 1m
      repeat_interval: 15m

receivers:
  - name: 'hanuman-default'
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#hanuman-alerts'
        title: '🕉️ Hanuman Alert'
        text: |
          {{ range .Alerts }}
          *{{ .Annotations.summary }}*
          Component: {{ .Labels.component }}
          Severity: {{ .Labels.severity }}
          {{ .Annotations.description }}
          {{ end }}

  - name: 'hanuman-critical'
    email_configs:
      - to: '<EMAIL>'
        subject: '🚨 [CRITICAL] Hanuman Alert'
        body: |
          🚨 ALERTE CRITIQUE HANUMAN 🚨

          {{ range .Alerts }}
          Component: {{ .Labels.component }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Auto-Action: {{ .Annotations.auto_action }}
          Runbook: {{ .Annotations.runbook }}
          {{ end }}
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#hanuman-critical'
        title: '🚨 CRITICAL: Hanuman System Alert'
        text: |
          {{ range .Alerts }}
          🚨 *{{ .Annotations.summary }}*
          Component: {{ .Labels.component }}
          {{ .Annotations.description }}
          Auto-correction: {{ .Annotations.auto_action }}
          {{ end }}

  - name: 'hanuman-security-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '🛡️ [SECURITY] Hanuman Security Alert'
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#security-alerts'
        title: '🛡️ Security Alert - Hanuman'

  - name: 'hanuman-devops-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '⚙️ [DEVOPS] Hanuman Infrastructure Alert'
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#devops-alerts'
        title: '⚙️ DevOps Alert - Hanuman'

  - name: 'hanuman-predictive'
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#hanuman-predictions'
        title: '🔮 Predictive Alert - Hanuman'
        text: |
          {{ range .Alerts }}
          🔮 *Prédiction: {{ .Annotations.summary }}*
          {{ .Annotations.description }}
          Action préventive: {{ .Annotations.auto_action }}
          {{ end }}

  - name: 'hanuman-anomaly-team'
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#hanuman-anomalies'
        title: '🧠 Anomaly Detected - Hanuman'
        text: |
          {{ range .Alerts }}
          🧠 *Anomalie: {{ .Annotations.summary }}*
          {{ .Annotations.description }}
          Analyse requise: {{ .Annotations.auto_action }}
          {{ end }}

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'component']
EOF

    log "✅ Escalation intelligente configurée"
}

# Configuration du dashboard de monitoring avancé
configure_monitoring_dashboard() {
    log "📊 Configuration du dashboard de monitoring avancé..."

    # Dashboard Grafana pour Hanuman
    cat > "$ALERTS_DIR/dashboards/hanuman-predictive-dashboard.json" << 'EOF'
{
  "dashboard": {
    "id": null,
    "title": "🕉️ Hanuman - Predictive Monitoring Dashboard",
    "tags": ["hanuman", "predictive", "ai"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "🧠 Cortex Central Health",
        "type": "stat",
        "targets": [
          {
            "expr": "up{job=\"cortex-central\"}",
            "legendFormat": "Cortex Status"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "color": {
              "mode": "thresholds"
            },
            "thresholds": {
              "steps": [
                {"color": "red", "value": 0},
                {"color": "green", "value": 1}
              ]
            }
          }
        },
        "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}
      },
      {
        "id": 2,
        "title": "🤖 Specialized Agents Status",
        "type": "stat",
        "targets": [
          {
            "expr": "count(up{job=~\"agent-.*\"} == 1)",
            "legendFormat": "Active Agents"
          }
        ],
        "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}
      },
      {
        "id": 3,
        "title": "🔮 Predictive Alerts Timeline",
        "type": "timeseries",
        "targets": [
          {
            "expr": "increase(prometheus_notifications_total{instance=\"alertmanager\"}[5m])",
            "legendFormat": "Predictive Alerts"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
      },
      {
        "id": 4,
        "title": "💾 Memory Usage Prediction",
        "type": "timeseries",
        "targets": [
          {
            "expr": "predict_linear(memory_usage_bytes[15m], 600)",
            "legendFormat": "Predicted Memory (10min)"
          },
          {
            "expr": "memory_usage_bytes",
            "legendFormat": "Current Memory"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
      },
      {
        "id": 5,
        "title": "🖥️ CPU Usage Prediction",
        "type": "timeseries",
        "targets": [
          {
            "expr": "predict_linear(cpu_usage_percent[10m], 300)",
            "legendFormat": "Predicted CPU (5min)"
          },
          {
            "expr": "cpu_usage_percent",
            "legendFormat": "Current CPU"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
      },
      {
        "id": 6,
        "title": "🚨 Active Anomalies",
        "type": "table",
        "targets": [
          {
            "expr": "ALERTS{alertstate=\"firing\", anomaly=\"true\"}",
            "format": "table"
          }
        ],
        "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}
      },
      {
        "id": 7,
        "title": "🔧 Auto-Correction Actions",
        "type": "logs",
        "targets": [
          {
            "expr": "{job=\"hanuman-auto-correction\"}",
            "legendFormat": "Auto-Correction Logs"
          }
        ],
        "gridPos": {"h": 8, "w": 24, "x": 0, "y": 24}
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "30s"
  }
}
EOF

    # Script de déploiement du dashboard
    cat > "$ALERTS_DIR/scripts/deploy-dashboard.sh" << 'EOF'
#!/bin/bash

# 📊 Déploiement du Dashboard Hanuman
# ==================================

GRAFANA_URL="${GRAFANA_URL:-http://localhost:3000}"
GRAFANA_API_KEY="${GRAFANA_API_KEY:-admin:admin}"

echo "📊 Déploiement du dashboard Hanuman..."

# Créer le dashboard via l'API Grafana
curl -X POST \
  "$GRAFANA_URL/api/dashboards/db" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $GRAFANA_API_KEY" \
  -d @hanuman-predictive-dashboard.json

echo "✅ Dashboard déployé avec succès!"
EOF

    chmod +x "$ALERTS_DIR/scripts/deploy-dashboard.sh"

    log "✅ Dashboard de monitoring avancé configuré"
}

# Configuration des health checks avancés
configure_advanced_health_checks() {
    log "🏥 Configuration des health checks avancés..."

    # Service de health checks avec IA
    cat > "$ALERTS_DIR/health-checks.js" << 'EOF'
/**
 * 🏥 HANUMAN - Health Checks Avancés
 * =================================
 * Système de surveillance de santé avec IA prédictive
 */

const http = require('http');
const https = require('https');
const { exec } = require('child_process');
const AnomalyDetectionAI = require('./anomaly-detection');

class AdvancedHealthChecker {
    constructor() {
        this.anomalyDetector = new AnomalyDetectionAI();
        this.healthHistory = [];
        this.checkInterval = 5000; // 5 secondes
        this.services = [
            {
                name: 'cortex-central',
                url: 'http://localhost:3001/health',
                critical: true,
                timeout: 2000
            },
            {
                name: 'specialized-agents',
                url: 'http://localhost:3002/health',
                critical: true,
                timeout: 3000
            },
            {
                name: 'immune-system',
                url: 'http://localhost:3003/health',
                critical: false,
                timeout: 2000
            }
        ];
    }

    // Démarrer la surveillance continue
    startContinuousMonitoring() {
        console.log('🏥 Démarrage des health checks avancés...');

        setInterval(async () => {
            await this.performHealthChecks();
        }, this.checkInterval);

        // Health check initial
        this.performHealthChecks();
    }

    // Effectuer tous les health checks
    async performHealthChecks() {
        const timestamp = new Date().toISOString();
        const healthReport = {
            timestamp,
            services: [],
            systemMetrics: await this.getSystemMetrics(),
            overallHealth: 'healthy'
        };

        // Vérifier chaque service
        for (const service of this.services) {
            const serviceHealth = await this.checkServiceHealth(service);
            healthReport.services.push(serviceHealth);

            if (serviceHealth.status === 'unhealthy' && service.critical) {
                healthReport.overallHealth = 'critical';
            } else if (serviceHealth.status === 'degraded') {
                healthReport.overallHealth = 'degraded';
            }
        }

        // Analyser avec l'IA
        const anomalies = await this.anomalyDetector.analyzeMetrics(healthReport.systemMetrics);
        healthReport.anomalies = anomalies;

        // Stocker l'historique
        this.healthHistory.push(healthReport);
        if (this.healthHistory.length > 1000) {
            this.healthHistory = this.healthHistory.slice(-1000);
        }

        // Déclencher des alertes si nécessaire
        await this.triggerAlertsIfNeeded(healthReport);

        return healthReport;
    }

    // Vérifier la santé d'un service spécifique
    async checkServiceHealth(service) {
        const startTime = Date.now();

        try {
            const response = await this.makeHttpRequest(service.url, service.timeout);
            const responseTime = Date.now() - startTime;

            let status = 'healthy';
            if (responseTime > service.timeout * 0.8) {
                status = 'degraded';
            }

            return {
                name: service.name,
                status: status,
                responseTime: responseTime,
                statusCode: response.statusCode,
                critical: service.critical,
                lastCheck: new Date().toISOString()
            };
        } catch (error) {
            return {
                name: service.name,
                status: 'unhealthy',
                error: error.message,
                critical: service.critical,
                lastCheck: new Date().toISOString()
            };
        }
    }

    // Faire une requête HTTP avec timeout
    makeHttpRequest(url, timeout) {
        return new Promise((resolve, reject) => {
            const request = (url.startsWith('https') ? https : http).get(url, (response) => {
                resolve(response);
            });

            request.setTimeout(timeout, () => {
                request.abort();
                reject(new Error('Request timeout'));
            });

            request.on('error', reject);
        });
    }

    // Obtenir les métriques système
    async getSystemMetrics() {
        return new Promise((resolve) => {
            exec('top -l 1 | head -n 10', (error, stdout) => {
                if (error) {
                    resolve({
                        cpu: 0,
                        memory: 0,
                        network: 0,
                        response_time: 0
                    });
                    return;
                }

                // Parser les métriques système (simplifié)
                const metrics = {
                    cpu: Math.random() * 100, // Simulation
                    memory: Math.random() * 100,
                    network: Math.random() * 1000,
                    response_time: Math.random() * 500
                };

                resolve(metrics);
            });
        });
    }

    // Déclencher des alertes si nécessaire
    async triggerAlertsIfNeeded(healthReport) {
        // Alertes pour services critiques en panne
        const criticalDown = healthReport.services.filter(s =>
            s.critical && s.status === 'unhealthy'
        );

        if (criticalDown.length > 0) {
            await this.sendAlert('critical', `Services critiques en panne: ${criticalDown.map(s => s.name).join(', ')}`);
        }

        // Alertes pour anomalies détectées
        const criticalAnomalies = healthReport.anomalies.filter(a => a.severity === 'critical');
        if (criticalAnomalies.length > 0) {
            await this.sendAlert('warning', `Anomalies critiques détectées: ${criticalAnomalies.length}`);
        }

        // Alertes prédictives
        for (const anomaly of healthReport.anomalies) {
            if (anomaly.prediction > anomaly.value * 1.5) {
                await this.sendAlert('prediction', `Dégradation prédite pour ${anomaly.metric}: ${anomaly.prediction}`);
            }
        }
    }

    // Envoyer une alerte
    async sendAlert(severity, message) {
        console.log(`🚨 [${severity.toUpperCase()}] ${message}`);

        // Ici, intégration avec le système d'alerting
        // (Prometheus AlertManager, Slack, etc.)
    }

    // Générer un rapport de santé
    generateHealthReport() {
        const recentChecks = this.healthHistory.slice(-10);
        const avgResponseTime = recentChecks.reduce((sum, check) => {
            const serviceAvg = check.services.reduce((s, service) =>
                s + (service.responseTime || 0), 0) / check.services.length;
            return sum + serviceAvg;
        }, 0) / recentChecks.length;

        return {
            timestamp: new Date().toISOString(),
            overallHealth: this.calculateOverallHealth(),
            averageResponseTime: avgResponseTime,
            totalChecks: this.healthHistory.length,
            recentAnomalies: this.anomalyDetector.anomalyHistory.slice(-5),
            recommendations: this.generateHealthRecommendations()
        };
    }

    // Calculer la santé globale
    calculateOverallHealth() {
        if (this.healthHistory.length === 0) return 'unknown';

        const recent = this.healthHistory.slice(-5);
        const criticalIssues = recent.filter(h => h.overallHealth === 'critical').length;
        const degradedIssues = recent.filter(h => h.overallHealth === 'degraded').length;

        if (criticalIssues > 2) return 'critical';
        if (degradedIssues > 3) return 'degraded';
        return 'healthy';
    }

    // Générer des recommandations de santé
    generateHealthRecommendations() {
        const recommendations = [];
        const recentHealth = this.healthHistory.slice(-10);

        // Analyser les patterns de performance
        const slowServices = {};
        recentHealth.forEach(health => {
            health.services.forEach(service => {
                if (service.responseTime > 1000) {
                    slowServices[service.name] = (slowServices[service.name] || 0) + 1;
                }
            });
        });

        for (const [service, count] of Object.entries(slowServices)) {
            if (count > 3) {
                recommendations.push({
                    type: 'performance',
                    service: service,
                    issue: `Service lent détecté ${count} fois`,
                    recommendation: 'Optimiser les performances ou augmenter les ressources'
                });
            }
        }

        return recommendations;
    }
}

module.exports = AdvancedHealthChecker;

// Démarrage automatique si exécuté directement
if (require.main === module) {
    const healthChecker = new AdvancedHealthChecker();
    healthChecker.startContinuousMonitoring();
}
EOF

    log "✅ Health checks avancés configurés"
}

# Fonction principale
main() {
    log "🚀 Démarrage de la configuration des alertes prédictives..."

    check_prerequisites
    create_directory_structure
    configure_predictive_rules
    configure_auto_correction
    configure_anomaly_detection_ai
    configure_intelligent_escalation
    configure_monitoring_dashboard
    configure_advanced_health_checks

    log "🎉 Configuration des alertes prédictives terminée avec succès!"
    log "📊 Dashboard disponible: http://localhost:3000/d/hanuman-predictive"
    log "🔧 Auto-correction activée: $ALERTS_DIR/scripts/auto-correction.sh"
    log "🧠 IA d'anomalies: $ALERTS_DIR/anomaly-detection.js"
    log "🏥 Health checks: $ALERTS_DIR/health-checks.js"
}

# Exécution du script principal
main "$@"
