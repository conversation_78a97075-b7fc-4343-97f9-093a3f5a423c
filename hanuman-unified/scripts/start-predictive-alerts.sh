#!/bin/bash

# 🚀 HANUMAN - Démarrage des Alertes Prédictives
# ==============================================
# Script pour démarrer le système d'alerting prédictif en production

set -e

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration des chemins
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
HANUMAN_ROOT="$PROJECT_ROOT/hanuman-unified"
MONITORING_DIR="$HANUMAN_ROOT/monitoring"
ALERTS_DIR="$MONITORING_DIR/predictive-alerts"

echo -e "${PURPLE}🚀 HANUMAN - Démarrage Alertes Prédictives${NC}"
echo -e "${PURPLE}===========================================${NC}"
echo -e "${CYAN}🎯 Activation du système d'alerting en production${NC}"
echo ""

# Fonction de logging
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}" >&2
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# Vérification des prérequis
check_prerequisites() {
    log "🔍 Vérification des prérequis..."
    
    # Vérifier que la configuration existe
    if [[ ! -d "$ALERTS_DIR" ]]; then
        error "Configuration des alertes prédictives non trouvée"
        error "Exécutez d'abord: ./scripts/configure-predictive-alerts.sh"
        exit 1
    fi
    
    # Vérifier Node.js
    if ! command -v node &> /dev/null; then
        error "Node.js n'est pas installé"
        exit 1
    fi
    
    # Vérifier les fichiers essentiels
    local essential_files=(
        "$ALERTS_DIR/anomaly-detection.js"
        "$ALERTS_DIR/health-checks.js"
        "$ALERTS_DIR/scripts/auto-correction.sh"
    )
    
    for file in "${essential_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            error "Fichier essentiel manquant: $(basename "$file")"
            exit 1
        fi
    done
    
    success "Prérequis validés"
}

# Démarrage de l'IA de détection d'anomalies
start_anomaly_detection() {
    log "🧠 Démarrage de l'IA de détection d'anomalies..."
    
    # Créer un script de démarrage pour l'IA
    cat > "$ALERTS_DIR/start-anomaly-ai.js" << 'EOF'
const AnomalyDetectionAI = require('./anomaly-detection.js');

console.log('🧠 Démarrage de l\'IA de détection d\'anomalies Hanuman...');

const ai = new AnomalyDetectionAI();

// Simuler des métriques en continu
setInterval(async () => {
    // Obtenir des métriques simulées (en production, connecter aux vraies métriques)
    const metrics = {
        cpu: Math.random() * 100,
        memory: Math.random() * 100,
        network: Math.random() * 1000,
        response_time: Math.random() * 500
    };
    
    // Ajouter aux données d'apprentissage
    for (const [metric, value] of Object.entries(metrics)) {
        ai.addLearningData(metric, value);
    }
    
    // Analyser les anomalies
    const anomalies = await ai.analyzeMetrics(metrics);
    
    if (anomalies.length > 0) {
        console.log(`🚨 ${anomalies.length} anomalie(s) détectée(s):`, anomalies);
        
        // Déclencher des actions d'auto-correction si nécessaire
        for (const anomaly of anomalies) {
            if (anomaly.severity === 'critical') {
                console.log(`🔧 Déclenchement auto-correction pour ${anomaly.metric}`);
                // Ici, déclencher l'auto-correction
            }
        }
    }
    
    // Générer un rapport toutes les 5 minutes
    if (Date.now() % 300000 < 5000) {
        const report = ai.generateAnomalyReport();
        console.log('📊 Rapport d\'anomalies:', report);
    }
    
}, 5000); // Toutes les 5 secondes

console.log('✅ IA de détection d\'anomalies active');
EOF
    
    # Démarrer l'IA en arrière-plan
    nohup node "$ALERTS_DIR/start-anomaly-ai.js" > "$HANUMAN_ROOT/logs/alerts/anomaly-ai.log" 2>&1 &
    local ai_pid=$!
    echo $ai_pid > "$HANUMAN_ROOT/logs/alerts/anomaly-ai.pid"
    
    success "IA de détection d'anomalies démarrée (PID: $ai_pid)"
}

# Démarrage des health checks avancés
start_health_checks() {
    log "🏥 Démarrage des health checks avancés..."
    
    # Créer un script de démarrage pour les health checks
    cat > "$ALERTS_DIR/start-health-checks.js" << 'EOF'
const AdvancedHealthChecker = require('./health-checks.js');

console.log('🏥 Démarrage des health checks avancés Hanuman...');

const healthChecker = new AdvancedHealthChecker();

// Démarrer la surveillance continue
healthChecker.startContinuousMonitoring();

// Générer un rapport de santé toutes les 10 minutes
setInterval(() => {
    const report = healthChecker.generateHealthReport();
    console.log('📊 Rapport de santé:', JSON.stringify(report, null, 2));
}, 600000); // 10 minutes

console.log('✅ Health checks avancés actifs');
EOF
    
    # Démarrer les health checks en arrière-plan
    nohup node "$ALERTS_DIR/start-health-checks.js" > "$HANUMAN_ROOT/logs/alerts/health-checks.log" 2>&1 &
    local health_pid=$!
    echo $health_pid > "$HANUMAN_ROOT/logs/alerts/health-checks.pid"
    
    success "Health checks avancés démarrés (PID: $health_pid)"
}

# Configuration des variables d'environnement
setup_environment() {
    log "🔧 Configuration de l'environnement..."
    
    # Créer un fichier d'environnement pour les alertes
    cat > "$ALERTS_DIR/.env" << EOF
# Configuration Hanuman Alertes Prédictives
NODE_ENV=production
HANUMAN_ROOT=$HANUMAN_ROOT
ALERTS_DIR=$ALERTS_DIR
LOG_LEVEL=info

# Configuration Slack (à personnaliser)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/WEBHOOK/URL

# Configuration Grafana (à personnaliser)
GRAFANA_URL=http://localhost:3000
GRAFANA_API_KEY=your-grafana-api-key

# Configuration PagerDuty (à personnaliser)
PAGERDUTY_INTEGRATION_KEY=your-pagerduty-key

# Seuils d'alerting personnalisés
CPU_WARNING_THRESHOLD=70
CPU_CRITICAL_THRESHOLD=85
MEMORY_WARNING_THRESHOLD=80
MEMORY_CRITICAL_THRESHOLD=90
RESPONSE_TIME_WARNING_THRESHOLD=500
RESPONSE_TIME_CRITICAL_THRESHOLD=1000
EOF
    
    success "Environnement configuré"
}

# Affichage du statut du système
show_system_status() {
    log "📊 Affichage du statut du système..."
    
    echo ""
    echo -e "${CYAN}🕉️  HANUMAN - Statut des Alertes Prédictives${NC}"
    echo -e "${CYAN}=============================================${NC}"
    echo ""
    
    # Vérifier les processus
    if [[ -f "$HANUMAN_ROOT/logs/alerts/anomaly-ai.pid" ]]; then
        local ai_pid=$(cat "$HANUMAN_ROOT/logs/alerts/anomaly-ai.pid")
        if ps -p $ai_pid > /dev/null 2>&1; then
            echo -e "${GREEN}🧠 IA Détection Anomalies: ✅ ACTIF (PID: $ai_pid)${NC}"
        else
            echo -e "${RED}🧠 IA Détection Anomalies: ❌ ARRÊTÉ${NC}"
        fi
    else
        echo -e "${YELLOW}🧠 IA Détection Anomalies: ⚠️  NON DÉMARRÉ${NC}"
    fi
    
    if [[ -f "$HANUMAN_ROOT/logs/alerts/health-checks.pid" ]]; then
        local health_pid=$(cat "$HANUMAN_ROOT/logs/alerts/health-checks.pid")
        if ps -p $health_pid > /dev/null 2>&1; then
            echo -e "${GREEN}🏥 Health Checks: ✅ ACTIF (PID: $health_pid)${NC}"
        else
            echo -e "${RED}🏥 Health Checks: ❌ ARRÊTÉ${NC}"
        fi
    else
        echo -e "${YELLOW}🏥 Health Checks: ⚠️  NON DÉMARRÉ${NC}"
    fi
    
    # Vérifier les logs récents
    echo ""
    echo -e "${BLUE}📋 Logs récents:${NC}"
    if [[ -f "$HANUMAN_ROOT/logs/alerts/anomaly-ai.log" ]]; then
        echo -e "${BLUE}   🧠 IA Anomalies: $(tail -1 "$HANUMAN_ROOT/logs/alerts/anomaly-ai.log" 2>/dev/null || echo "Aucun log")${NC}"
    fi
    if [[ -f "$HANUMAN_ROOT/logs/alerts/health-checks.log" ]]; then
        echo -e "${BLUE}   🏥 Health Checks: $(tail -1 "$HANUMAN_ROOT/logs/alerts/health-checks.log" 2>/dev/null || echo "Aucun log")${NC}"
    fi
    
    echo ""
    echo -e "${CYAN}📊 Dashboard: http://localhost:3000/d/hanuman-predictive${NC}"
    echo -e "${CYAN}📁 Logs: $HANUMAN_ROOT/logs/alerts/${NC}"
    echo -e "${CYAN}🔧 Configuration: $ALERTS_DIR${NC}"
    echo ""
}

# Fonction principale
main() {
    log "🚀 Démarrage du système d'alertes prédictives Hanuman..."
    
    check_prerequisites
    setup_environment
    start_anomaly_detection
    start_health_checks
    
    sleep 2  # Laisser le temps aux processus de démarrer
    
    show_system_status
    
    echo -e "${GREEN}🎉 Système d'alertes prédictives Hanuman démarré avec succès!${NC}"
    echo -e "${BLUE}💡 Utilisez './scripts/stop-predictive-alerts.sh' pour arrêter le système${NC}"
    echo -e "${BLUE}💡 Utilisez './scripts/status-predictive-alerts.sh' pour voir le statut${NC}"
}

# Exécution du script principal
main "$@"
